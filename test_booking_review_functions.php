<?php
/**
 * File test các hàm chức năng mới trong BookingReviewModel
 * Chạy file này để kiểm tra các hàm có hoạt động đúng không
 */

// Include autoloader và bootstrap của Phalcon (điều chỉnh path cho phù hợp)
require_once __DIR__ . '/apps/config/loader.php';
require_once __DIR__ . '/apps/config/services.php';

use Modules\App\Models\BookingReviewModel;
use Modules\App\Models\HostingRatingModel;

echo "=== TEST CÁC HÀNG CHỨC NĂNG BOOKING REVIEW ===\n\n";

// Khởi tạo model
$reviewModel = new BookingReviewModel();

// Test với hosting ID = 1 (thay đổi theo dữ liệu thực tế)
$hostingId = 1;

echo "1. Test getReviewsByHosting($hostingId):\n";
$reviews = $reviewModel->getReviewsByHosting($hostingId, ['limit' => 5]);
echo "Số lượng đánh giá: " . count($reviews) . "\n";
foreach ($reviews as $review) {
    echo "- Review ID: {$review->id}, Overall: {$review->overall}, Created: {$review->created}\n";
}
echo "\n";

echo "2. Test calculateHostingRating($hostingId):\n";
$rating = $reviewModel->calculateHostingRating($hostingId);
if ($rating) {
    echo "Tổng đánh giá: {$rating['total_reviews']}\n";
    echo "Điểm tổng thể: {$rating['overall_avg']}\n";
    echo "Điểm sạch sẽ: {$rating['clean_avg']}\n";
    echo "Điểm nhân viên: {$rating['staff_avg']}\n";
    echo "Điểm tiện nghi: {$rating['amenities_avg']}\n";
    echo "Điểm vị trí: {$rating['location_avg']}\n";
    echo "Điểm giá trị: {$rating['value_avg']}\n";
} else {
    echo "Không có đánh giá nào\n";
}
echo "\n";

echo "3. Test getHostingOverallRating($hostingId):\n";
$overallRating = $reviewModel->getHostingOverallRating($hostingId);
echo "Điểm tổng thể: " . ($overallRating ?? 'Không có') . "\n\n";

echo "4. Test getHostingRatingDistribution($hostingId):\n";
$distribution = $reviewModel->getHostingRatingDistribution($hostingId);
echo "Phân bố điểm:\n";
for ($i = 5; $i >= 1; $i--) {
    echo "- {$i} sao: {$distribution[$i]} đánh giá\n";
}
echo "\n";

echo "5. Test getTopPositiveReviews($hostingId, 3):\n";
$topReviews = $reviewModel->getTopPositiveReviews($hostingId, 3);
echo "Top 3 đánh giá tích cực:\n";
foreach ($topReviews as $review) {
    echo "- Review ID: {$review->id}, Overall: {$review->overall}, Title: " . substr($review->title, 0, 50) . "...\n";
}
echo "\n";

echo "6. Test getLatestReviews($hostingId, 3):\n";
$latestReviews = $reviewModel->getLatestReviews($hostingId, 3);
echo "3 đánh giá mới nhất:\n";
foreach ($latestReviews as $review) {
    echo "- Review ID: {$review->id}, Created: {$review->created}, Overall: {$review->overall}\n";
}
echo "\n";

echo "7. Test getMonthlyRatingStats($hostingId, 6):\n";
$monthlyStats = $reviewModel->getMonthlyRatingStats($hostingId, 6);
echo "Thống kê 6 tháng gần đây:\n";
foreach ($monthlyStats as $stat) {
    echo "- {$stat['month']}/{$stat['year']}: {$stat['total_reviews']} đánh giá, điểm TB: {$stat['avg_rating']}\n";
}
echo "\n";

echo "8. Test compareWithSimilarHostings($hostingId):\n";
$comparison = $reviewModel->compareWithSimilarHostings($hostingId);
if ($comparison) {
    echo "So sánh với hosting cùng loại:\n";
    echo "- Điểm hiện tại: {$comparison['current']['overall_avg']}\n";
    echo "- Điểm trung bình cùng loại: {$comparison['similar_avg']['overall_avg']}\n";
    echo "- Chênh lệch: " . ($comparison['comparison']['overall'] > 0 ? '+' : '') . "{$comparison['comparison']['overall']}\n";
} else {
    echo "Không thể so sánh (không có dữ liệu hoặc hosting không tồn tại)\n";
}
echo "\n";

echo "9. Test getMostMentionedKeywords($hostingId, 5):\n";
$keywords = $reviewModel->getMostMentionedKeywords($hostingId, 5);
echo "Top 5 từ khóa được nhắc nhiều nhất:\n";
foreach ($keywords as $word => $count) {
    echo "- '{$word}': {$count} lần\n";
}
echo "\n";

echo "10. Test updateHostingRatingTable($hostingId):\n";
$updateResult = $reviewModel->updateHostingRatingTable($hostingId);
echo "Kết quả cập nhật bảng hosting_rating: " . ($updateResult ? 'Thành công' : 'Thất bại') . "\n";

// Kiểm tra dữ liệu trong bảng hosting_rating
$hostingRating = HostingRatingModel::findFirst([
    'conditions' => 'hosting_id = :hosting_id:',
    'bind' => ['hosting_id' => $hostingId]
]);

if ($hostingRating) {
    echo "Dữ liệu trong bảng hosting_rating:\n";
    echo "- Tổng đánh giá: {$hostingRating->total_reviews}\n";
    echo "- Điểm tổng thể: {$hostingRating->overall_avg}\n";
    echo "- Điểm sạch sẽ: {$hostingRating->clean_avg}\n";
    echo "- Điểm nhân viên: {$hostingRating->staff_avg}\n";
    echo "- Điểm tiện nghi: {$hostingRating->amenities_avg}\n";
    echo "- Điểm vị trí: {$hostingRating->location_avg}\n";
    echo "- Điểm giá trị: {$hostingRating->value_avg}\n";
    echo "- Cập nhật lần cuối: {$hostingRating->updated}\n";
} else {
    echo "Không tìm thấy dữ liệu trong bảng hosting_rating\n";
}

echo "\n=== KẾT THÚC TEST ===\n";
