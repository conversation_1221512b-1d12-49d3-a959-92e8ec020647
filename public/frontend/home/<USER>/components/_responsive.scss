/*------------------------------------------------
** MEDIA ONLY SCREEN
**------------------------------------------------*/
/* Mbile & Tablet */
@media (max-width: 1200px) {
  .hide-1200 {
    display: none !important;
  }

  .full-1200 {
    flex: 100% !important;
    max-width: 100% !important;
  }

  .btn-filter {
    display: block !important;
  }

  .filter__cont {
    position: fixed;
    top: 68px;
    left: 0;
    max-width: 450px !important;
    width: 100%;
    height: calc(100vh - 68px);
    background-color: #fff;
    z-index: 9998;
    box-shadow: 1px 0px 5px 0px rgba(0, 0, 0, 0.1);
    transform: translateX(-100%);
    transition: all 0.3s ease-in-out !important;
    padding-top: 40px;
    visibility: hidden !important;
    display: block !important;

    .close {
      display: flex !important;
      position: absolute;
      top: 10px;
      right: 20px;
      width: 30px;
      height: 30px;
      display: flex !important;
      justify-content: center;
      align-items: center;
      cursor: pointer;
      // border-radius: 50%;
      border: 1px solid #000;
      background-color: #fff;
    }

    &.active {
      visibility: visible !important;
      transform: translateX(0) !important;

      .sidebar {
        height: 100%;
        overflow: auto;
      }
    }
  }

  .select2-container {
    z-index: 9999;
  }

  .home .bg-section {
    border: none;
  }

  .host__head--frame {
    padding: 20px;
  }

  .host__count .item {
    &__icon {
      width: 60px;
      height: 60px;
    }

    &__frame {
      width: calc(100% - 80px);
    }

    &__number {
      font-size: 42px;
    }
  }

  .host__analytics .item__number {
    font-size: 60px;
  }
}

@media (max-width: 1023px) {
  .main-body {
    margin-top: 56px;
  }

  .hidden-on-mobile-table {
    display: none;
  }

  .rd-panel {
    display: block;
  }

  .box-head {
    padding-top: 10px;

    .box-head__title {
      font-size: 26px;
    }
  }

  .box-search__wrap {
    flex-direction: column;
    height: auto;
    border-radius: 0 !important;
    background-color: transparent;
    box-shadow: none;
    padding: 10px 0;
    gap: 8px;

    .search-list {
      flex-direction: column;

      >* {
        width: 100% !important;
        border-radius: 0 !important;
        height: 64px !important;
        border-radius: 32px !important;
        border: 1px solid #eee;
        padding: 0 !important;
        margin-bottom: 10px;
        background: #fff !important;
        box-shadow: 2px 4px 12px 0px rgba(52, 61, 55, 0.06) !important;

        &:last-child {
          margin-bottom: 0;
        }
      }

      .item {
        &.search-input {
          min-width: auto;

          i {
            top: 0;
            left: 0;
          }
        }

        .item-icon {
          padding: 10px;
        }
      }

      .line {
        display: none !important;
      }
    }

    .search-submit__btn {
      padding: 12px 20px;
      font-size: 15px;
    }
  }

  .box-about {
    padding: 16px 0;

    .item {
      margin-bottom: 10px;
    }
  }

  .list-province {
    overflow-x: auto;
    gap: 10px;

    &::-webkit-scrollbar {
      display: none;
    }

    .item-name {
      white-space: nowrap;
      font-size: 12px;
      padding: 12px;
    }
  }

  .box-videos {
    padding: 30px 0;
  }

  .page.review {
    .bg-section {
      padding: 20px 0;
    }
  }

  .form-group label {
    font-size: 13px;
  }

  .form-group input,
  .form-group textarea,
  .form-group select {
    padding: 8px 20px;
  }

  #contact,
  .user-page {
    .sidebar {
      overflow-x: auto;
      padding-bottom: 8px;
    }
  }

  .list-tab {
    display: flex;
    flex-direction: row;
    gap: 10px;

    .tab {
      flex: 0 0 calc((100% - 20px) / 3);
      min-width: fit-content;
      justify-content: center;
      margin-bottom: 0;
      padding: 8px 10px;
    }
  }

  .box-support {
    padding-top: 20px;

    .item {
      gap: 12px;
    }
  }

  .box-support .item__content--title {
    font-size: 16px;
  }

  .box-testimonial__slider .item__avt .image {
    width: 80px;
    height: 80px;
  }

  .filter__cont {
    top: 56px;
    height: calc(100vh - 56px);
  }

  .home-detail .box-header__flex {
    .box-header__wrap {
      width: 100%;
    }
  }

  .hidden-1023 {
    display: none;
  }

  .home-detail .box-header .box-address {
    margin-bottom: 0;
  }

  .home-detail .box-header__flex {
    flex-wrap: wrap;
  }

  .home-detail__price {
    align-items: flex-start;
  }

  .home-detail .box-header__wrap {
    .box-header__inline {
      flex-direction: column-reverse;
      align-items: flex-start;
    }

    .box-address {
      display: block;
    }

    .box-action {
      justify-content: left;
    }
  }

  .home-detail__price--main {
    margin: 0 0 20px;
  }

  .home-detail .box-home__review {
    margin: 0;
    // padding: 20px 0 0;
  }

  .show-1023 {
    display: block;
  }

  .hidden-1023 {
    display: none;
  } 

  .user-profile__tables .tables {
    &-row {
      border: none;
    }

    &-item {
      width: 100%;
      border: none;
    }
  }

  .box-benefits__nav .inner .item {
    padding: 10px 20px;
    border-radius: 30px;
    background: #fff !important;
    box-shadow: 2px 4px 12px 0px rgba(52, 61, 55, 0.06) !important;
    border: 1px solid #eee;
  }

  .host__head {
    &--banner .image img {
      position: initial;
      max-width: 450px;
      margin: auto;
      aspect-ratio: 1/1;
    }

    &--content {
      text-align: center;
    }
  }

  .host.index .section {
    padding: 20px 0;
  }

  .host__count .item {
    padding: 12px 20px;
    border-radius: 12px;

    &__icon {
      width: 40px;
      height: 40px;
    }

    &__frame {
      width: calc(100% - 60px);
    }

    &__number {
      font-size: 28px;
    }

    &__title {
      font-size: 13px;
    }
  }

  .host__about--content .block__list li {
    .icon {
      width: 20px;
    }

    p {
      width: calc(100% - 32px);
    }
  }

  .host__about {
    &--video img {
      max-width: 450px;
      margin: 0 auto;
      position: initial;
      aspect-ratio: 1/1;
    }

    &--frame {
      padding: 0 20px;
    }
  }

  .host__advantage {
    margin: 20px 0;

    &--frame .item {
      &__icon img {
        width: 60px;
      }
    }
  }

  .host.index .box-action {
    margin: 20px 0 0;
  }

  .host {
    .main-body {
      margin-top: 16px;
    }

    header .header__menu .item {
      height: 56px;

      &__link {
        flex-direction: row;
      }
    }

    header .header__menu .item__link {
      padding: 10px;
      justify-content: flex-start;
    }

    header .header__menu .item__title {
      font-size: 12px;
    }

    header .header-user__link {
      padding: 4px 0;

      .user {
        height: auto;

        &-wrap {
          padding: 3px 6px;
        }
      }
    }

    header .header__menu .item__icon i {
      font-size: 20px;
    }

    .header__menu--frame {
      .row {
        flex-wrap: nowrap;
      }

      // .c-4-5 {
      //   flex: 40%;
      // }
      .c-2-5 {
        flex: 20%;
        margin-left: 10px;
      }
    }

    .room__list .room__item {
      flex-direction: column;

      >* {
        width: 100%;
      }

      .room__item--thumb {
        img {
          width: 100%;
        }
      }
    }
  }

  .home-detail .box-gallery__wrap {
    flex-wrap: wrap;
    justify-content: center;
    margin-bottom: 20px;

    >* {
      width: 100% !important;
    }

    .box-gallery__home {
      margin-bottom: 0;
    }
  }

  .rd-search {
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background: #f3fae9;

    i {
      color: #024834;
      font-size: 18px;
    }

    &.active {
      background: #024834;

      i {
        color: #f3fae9;
      }
    }
  }

  .home {
    .box-search__container {
      visibility: hidden;
      position: fixed;
      top: -100%;
      left: 0;
      width: 100%;
      height: auto;
      z-index: 99;
      background: 0 3px 3px 0 rgba(0, 0, 0, 0.1);
      opacity: 0;
      @include _transition;

      .box-search__wrap {
        height: auto;

        .search-list {
          .search-input {
            input {
              color: $color_text;

              &::placeholder {
                color: $color_text_1;
              }
            }
          }

          .item {
            .item-title {
              color: $color_page_1;
            }

            .item-desc {
              color: $color_text_1;
            }
          }
        }
      }

      &.active {
        visibility: visible;
        top: 56px;
        opacity: 1;
      }
    }
  }

  .er_toc {
    max-width: 100%;
  }

  .home-detail .box-home__villa .villa__list .item .btn-villa-detail {
    margin: 12px 0;
  }

  .news {
    &-item {
      &__content {
        padding: 12px 0 12px 12px;

        &--title {
          h3 {
            font-size: 16px;
          }
        }
      }
    }

    &__featured {
      &--slider {
        .slide {
          &__main {
            &--wrap {
              padding: 0;
              width: 100%;
            }
          }

          &__title {
            font-size: 14px;
          }

          &__action {
            top: 12px;
            right: 12px;
            bottom: auto;
          }
        }
      }
    }
  }

  .home-detail .box-home__room .room__list .item,
  .home-detail .box-home__whole .item {
    .info-wrap {
      gap: 16px;
      flex-direction: column;

      .guest {
        display: flex;
        gap: 16px;

        .text-head {
          margin-bottom: 0;
        }

        &-wrap {
          gap: 16px;
          flex-direction: row;
          align-items: center;
        }
      }
    }

    .btn-booking-wrap {
      flex-direction: row;
    }

    .whole-action {
      display: flex;
      justify-content: space-between;
      align-items: center;
      gap: 16px;
    }
  }

  .mb__fixed--bottom {
    display: none;
  }

  .home-detail .nav__fixed.active {
    top: 56px;

    .mb__fixed--bottom {
      display: block;

      .nav__fixed--right {
        position: fixed;
        bottom: 0;
        left: 0;
        width: 100%;
        background: #fff;
        z-index: 99;
        height: 56px;
        padding: 0 10px;
        box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
      }
    }
  }

  .box-search {
    margin-bottom: 10px;
  }
}

/* Tablet */
@media (min-width: 740px) and (max-width: 1023px) {}

/* Mobile */
@media (max-width: 739px) {
  body {
    font-size: 14px;
  }

  .popup-inner {
    padding: 40px 20px;
    margin: 10px;
    max-width: calc(100% - 20px);
    border-radius: 10px;
  }

  .home-detail .popup__room--detail .popup-inner__wrap {
    padding: 0;
  }

  .box-header,
  .host.index .box-header {
    margin-bottom: 20px;
  }

  .box-header__title {
    font-size: 22px !important;
    line-height: 1.5;
  }

  .box-about {
    .item {
      padding: 10px;

      &-image {
        .image {
          width: 40px;
          aspect-ratio: 1/1;
          object-fit: contain;
          display: block;
        }
      }

      &-count {
        margin-bottom: 6px;

        .number {
          font-size: 22px;
          line-height: 1;
        }

        .plus {
          font-size: 16px;
          line-height: 1;
        }
      }
    }
  }

  .home-item__content--price span {
    font-size: 15px;
  }

  footer .footer-title {
    margin-bottom: 8px;
    font-size: 16px;
  }

  .main-body {
    background-color: #fff !important;
    margin-bottom: 40px;
  }

  .home .main-content {
    &.bg-section {
      padding: 0 !important;
    }
  }

  .bg-section {
    padding: 20px 10px;
  }

  .home-item__props {
    overflow-x: auto;

    &::-webkit-scrollbar {
      display: none;
    }

    li {
      i {
        font-size: 16px;
      }
    }
  }

  .home-detail {
    .box-gallery__home {
      grid-template-columns: repeat(2, 1fr) !important;
      grid-gap: 10px !important;
    }

    .box-rules__cont .rules__item {
      flex-direction: column;

      >* {
        width: 100% !important;
      }
    }
  }

  .home-detail .box-home__room .room__list .room__item {
    flex-wrap: wrap;
    padding: 12px;

    &--wrap {
      width: 100%;
      flex-direction: column;
      align-items: flex-start;
      gap: 10px;
    }

    &--info {
      .wrap {
        gap: 12px;
      }
    }

    &--thumb img {
      width: 100%;
      aspect-ratio: 16/9;
    }

    &--price {
      small {
        display: inline-block;
      }
    }
  }

  .box-action__wrap {
    padding: 20px;

    .item-bg {
      &__left {
        display: none;
      }

      &__right {
        width: 50%;
        right: 50%;
        transform: translateX(50%);
      }
    }

    .action {
      padding-top: 20px;
    }
  }

  .box-testimonial__slider {
    .slick-list {
      margin: 0 -5px;
    }

    .item {
      margin: 0 5px;
      padding: 10px 5px;

      &__avt {
        .image {
          width: 60px;
          height: 60px;
        }
      }

      &__content {
        padding-top: 12px;

        &--name {
          font-size: 16px;
        }

        &--rating {
          margin: 6px 0 8px;

          i {
            font-size: 10px;
          }
        }
      }
    }
  }

  .pagination {
    margin-top: 20px;
  }

  .filter__cont {
    width: 100%;
  }

  .home .sidebar-frame__header .title {
    font-size: 16px;
  }

  .line {
    padding: 20px 0;
  }

  .home-detail__price--main {
    font-size: 20px;
  }

  .user-profile__info {
    flex-wrap: wrap;

    .user-profile__inline {
      width: 100%;
    }
  }

  .user-page .user-profile__tables .tables-item {
    width: 100%;
  }

  .box-news__featured .item-news__link--index {
    display: none;
  }

  .user-page .home__manager--list .item {
    flex-direction: column;

    >* {
      width: 100%;
    }
  }

  .host__about--content .block .title {
    font-size: 18px;
  }

  .host__page {
    .bg-section {
      padding: 0;
    }
  }

  .host__advantage {
    margin: 0;

    &--frame {
      .item {
        padding: 16px 12px;

        &__wrap {
          .title {
            font-size: 18px;
            margin: 10px 0;
          }

          .title,
          .content {
            text-align: center;
          }
        }

        &__icon {
          img {
            margin: 0 auto;
          }
        }
      }
    }
  }

  .host__faq--slidedown .item {
    margin-bottom: 0;
  }

  .host {
    .box-action__wrap {
      padding: 0;

      .button {
        position: relative;
        z-index: 1;
      }
    }

    .action {
      padding-top: 20px;
    }
  }

  .host__page--dashboard {
    >.row {
      gap: 40px 0;
    }

    .sidebar,
    .main {
      gap: 40px 0;
    }
  }

  .host__analytics .item {
    &__number {
      font-size: 40px;
      margin: 12px 0;
    }
  }

  .filter__cont {
    .sidebar {
      padding: 20px;
    }
  }

  .popup-inner .close {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background-color: #fff;
  }

  .home-detail .box-home__room .room__list .item .table-wrap table,
  .home-detail .box-home__whole .whole__list .item .table-wrap table {
    display: block;
    border-collapse: collapse;

    thead {
      display: none;
    }

    tbody,
    tr,
    td {
      display: block;
      width: 100%;
    }

    tr {
      margin-bottom: 10px;
    }

    td {
      text-align: right;
      padding-left: 50%;
      position: relative;
      border: none !important;
      border: 1px solid #eee !important;
      border-bottom: none !important;

      &:first-child {
        border-top: none !important;
      }

      &:last-child {
        border-bottom: 1px solid #eee !important;
      }

      .info-list {
        li {
          width: 100%;
        }
      }

      &::before {
        content: attr(data-label);
        position: absolute;
        left: 6px;
        width: 45%;
        padding-right: 10px;
        white-space: nowrap;
        text-align: left;
        font-weight: bold;
      }
    }
  }

  .room__table,
  .room__table tbody,
  .room__table tr,
  .room__table td {
    display: block;
    width: 100%;
  }

  .room__table tr {
    margin-bottom: 15px;
  }

  .room__table td {
    text-align: right;
    padding-left: 50%;
    position: relative;
  }

  .room__table td::before {
    content: attr(data-label);
    position: absolute;
    left: 6px;
    width: 45%;
    padding-right: 10px;
    white-space: nowrap;
    text-align: left;
    font-weight: bold;
  }

  .room__table thead {
    display: none;
  }

  .page.account .main-body {
    margin-top: 56px;
    background-color: transparent !important;
  }

  .box-form__booking {
    flex-direction: column;
    width: 100% !important;

    .form-group {
      width: 100% !important;

      .dropdown {
        width: 100% !important;
        max-width: 100% !important;
      }
    }

    .action {
      button {
        width: 100%;
      }
    }
  }

  .page.booking .booking__form .form-hafl {
    flex-direction: column;
    margin-bottom: 20px;

    .form-group {
      margin-bottom: 0;
    }
  }

  .page.booking .payment-selector .payment-options .radio-option .radio-label {
    flex-direction: column;
  }

  .page.booking .box {
    padding: 12px !important;

    &.info__home {
      display: flex;
      gap: 12px;

      .image {
        width: 40%;

        img {
          width: 100%;
          height: 100%;
          aspect-ratio: 1/1;
        }
      }
    }
  }

  .home-detail .box-home__room .room__list .item .btn-booking-wrap {
    flex-direction: column;
    align-items: flex-end;
  }

  .home-detail .nav__fixed .menu__list li a {
    padding: 8px 16px;
  }
}

@media (max-width: 650px) {
  .rd-panel .rd-right__wrap .user-popup {
    position: fixed;
    border-top: 2px solid #eee;
    top: 56px;
    left: 0;
    border-radius: 0;
  }

  .hd-650 {
    display: none;
  }

  .sh-650 {
    display: block;
  }

  .host__manage--home .item {

    &-thumb,
    &-wrap {
      width: 100%;
    }
  }

  .home-detail .box-home__room .room__list .item {
    flex-direction: column;

    >* {
      width: 100% !important;
    }

    .content__property {
      flex-wrap: wrap;
    }

    .list-properties {
      flex: 1;
      width: 100%;
    }
  }

  .page-error {
    &__title {
      font-size: 20px;
    }

    &__count {
      font-size: 14px;
      padding: 12px 0 20px;
    }
  }

  .popup__video--review .popup-inner .close {
    top: 0;
    right: 0;
    width: 30px;
    height: 30px;
    border-top-right-radius: 8px;
    border-top-left-radius: 0;
    border-bottom-left-radius: 50%;
    border-bottom-right-radius: 0;

    i {
      font-size: 16px;
    }
  }

  .daterangepicker.cal-active {
    width: 100% !important;

    .drp-calendar {
      padding: 8px;
    }
  }
}

@media (max-width: 550px) {
  .box-home__vip .box-header {
    i {
      display: none;
    }

    .box-header__title {
      &::before {
        content: "\f06d";
        font-family: "FontAwesome";
        font-size: 20px;
        color: #ff5c00;
        margin-right: 3px;
      }
    }
  }

  .c19,
  .c20,
  .c21,
  .c22,
  .c23,
  .c24,
  .c25,
  .c26,
  .c27,
  .c28,
  .c29,
  .c30,
  .c31,
  .c32,
  .c33,
  .c34,
  .c35,
  .c36,
  .c37 {
    display: none;
  }
}

@media (max-width: 450px) {
  .box-header__title {
    font-size: 18px !important;
  }

  .box-contact__form .form .button {
    width: 100%;
    padding: 10px 20px;
  }

  .box-support .item {
    padding: 0 5px;
  }

  .box-support .item__image {
    width: 60px;
  }

  .box-action__wrap {
    .item-bg {
      &__right {
        width: 80%;
      }
    }
  }

  .box-benefits__nav .inner .item-wrap {
    gap: 12px;

    &__image {
      img {
        width: 34px;
      }
    }

    &__title {
      width: calc(100% - 46px);

      .title {
        font-size: 16px;
      }
    }
  }

  .breadcrumbs {
    padding: 12px 0;
  }

  .home .main-content .btn-filter a {
    padding: 6px 8px;
    border-radius: 4px;
  }

  .box-search__wrap .search-list .item-popup__action {
    margin-top: 16px;
  }

  .home-detail .box-header__inline span {
    i {
      font-size: 20px;
    }
  }

  .home-detail__price {
    &--action {

      .button {
        padding: 16px 20px;
      }
    }
  }

  .host__head {
    &--content {
      margin-top: 50px;
    }

    &--slider {
      height: 350px;

      &::before {
        height: 100px;
        bottom: -50px;
      }
    }
  }

  .host__about--content .block .title {
    font-size: 15px;
  }

  .host__about--content .block {
    margin-bottom: 20px;

    &__list li {
      .icon {
        display: none;
      }

      p {
        width: 100%;
        text-align: justify;
      }
    }

    .title {
      margin-bottom: 12px;
    }
  }

  .host__manage--home .item {
    &-action__post a {
      width: 100%;

      &.text {
        background: #f4f4f4;
      }
    }

    &-wrap {
      padding: 12px;
    }
  }

  .host__analytics .item {
    &__frame {
      padding: 8px;
    }

    &__number {
      margin: 8px 0;
      font-size: 30px;
      justify-content: center;
    }

    &__title,
    &__content {
      font-size: 15px;
      text-align: center;
    }
  }

  .box-home__favourite {
    margin: 0 -10px;

    &.bg-section {
      padding: 20px 10px !important;
    }
  }

  .news {
    &-item {
      &__thumb {
        width: 100%;
      }

      &__content {
        width: 100%;
        padding: 12px 8px 0;
      }
    }

    &__featured {
      &--list {
        .news-item {
          &__thumb {
            width: 130px;
          }

          &__content {
            width: calc(100% - 130px);

            &--title {
              h3 {
                font-size: 14px;
              }
            }
          }
        }
      }
    }

    &__latest {
      &--list {
        .news-item {
          &__thumb {
            width: 130px;
          }

          &__content {
            width: calc(100% - 130px);

            &--title {
              h3 {
                font-size: 14px;
              }
            }

            &--desc {
              font-size: 12px;
            }
          }
        }
      }
    }

    &__category {
      &--tab {
        .tab {
          &__label {
            padding: 8px 12px;

            h3 {
              font-size: 14px;
            }
          }
        }
      }

      &--main {
        padding: 20px 0 0;
      }

      &--articles {
        .block {
          .button {
            width: 100%;
          }
        }
      }
    }

    .bg-section {
      padding: 0;
      border-radius: 0;
      box-shadow: none;
    }
  }

  .login-banner .banner-content {
    flex-wrap: wrap;

    .icon-wrapper {
      width: 20%;
    }

    .text-content {
      width: 80%;
    }

    a {
      width: 100%;
    }
  }

  .home-detail .nav__fixed .menu__list li a {
    padding: 5px 10px;
  }

  .page.booking .payment__page .bank-info-container .bank-info-item {
    align-items: flex-start;

    .info-value {
      flex-direction: column;
      gap: 4px;
      align-items: flex-end;
    }
  }

  

  /* Payment responsive styles */
}