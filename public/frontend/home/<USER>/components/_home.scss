.home {
  &-detail {
    margin-bottom: 80px;

    .see-more {
      color: $color_page_1;
      font-size: 14px;
      font-weight: 500;

      &:hover {
        text-decoration: underline;
      }
    }

    .box-search .search-input {
      min-width: 280px;

      input {
        text-overflow: ellipsis;
      }
    }

    .grid.wide {
      max-width: 1170px !important;
    }

    &__main {
      display: flex;
      flex-wrap: wrap;
      gap: 20px;

      &--info,
      &--video {
        width: 100%;
      }
    }

    &__price {
      display: flex;
      align-items: flex-end;
      flex-direction: column;
      white-space: nowrap;

      &--title {
        font-weight: 300;
      }

      &--main {
        color: $color_page_2;
        background-color: #fff;
        font-size: 26px;
        font-weight: 700;
        line-height: 1;
        margin: 2px 0 12px;
        display: block;

        &.mg-b-0 {
          margin-bottom: 0;
        }

      }

      &--action {
        display: flex;
        gap: 12px;
        align-items: center;

        .button {
          background: $color_page;
          color: #fff;
          width: 100%;
          text-align: center;
          @include _transition;
          background-color: $color_page_3;
          border: 1px solid darken($color_page_3, 5%);
          padding: 14px 26px;
          gap: 8px;

          &:hover {
            background: lighten($color_page_3, $amount: 5%);
            box-shadow: 2px 3px 6px 0px rgba($color_page_3, 0.5);
          }
        }
      }
    }

    .box-home {
      &__vip {
        background: #fff;
        box-shadow: $box_shadow_1;
        border-radius: 12px;
        padding: 20px;
      }

      &__detail {
        .content-detail {
          overflow: hidden;

          .read-more {
            color: $color_page_1;
            font-weight: 500;

            &-btn {
              margin: 0;
              margin-top: 10px;
            }

            &:hover {
              text-decoration: underline;
            }
          }
        }
      }

      &__amenities {
        &--action {
          margin-top: 20px;
        }

        .item {
          display: flex;
          align-items: center;
          gap: 12px;

          &-icon {
            width: 24px;
            height: 24px;

            .image {
              display: block;
              width: 100%;
              height: 100%;
              object-fit: contain;
            }

            i {
              color: $color_page_1;
            }
          }

          &-title {
            font-weight: 600;
          }
        }
      }

      &__regula {
        .item {
          display: flex;
          gap: 20px;

          &-image {
            width: 50px;
            height: 50px;
            padding: 13px;
            background: #f3fae9;

            .image {
              display: block;
              width: 100%;
              height: 100%;
              object-fit: cover;
            }
          }

          &-content {
            width: calc(100% - 70px);

            &__title {
              font-size: 18px;
              font-weight: 600;
              margin-bottom: 4px;
            }

            &__desc {
              color: #827f7f;
              line-height: 1.6;
              white-space: pre-line;
            }
          }
        }
      }

      &__map {
        .map {
          iframe {
            width: 100%;
            height: 400px;
            border-radius: 8px;
          }
        }
      }

      &__arrange {
        .inner {
          margin: 0 -10px;

          .item {
            margin: 0 10px;
          }
        }
      }

      &__videos {
        .item {
          &-video {
            &__thumb {
              .image {
                aspect-ratio: 16/10;
              }
            }
          }

          &-content {
            padding-left: 0;

            &__address {
              color: #abaaaa;
            }

            &__title {
              color: $color_text;
            }

            &__desc {
              color: $color_text_1;
              @include _line_clamp(3);
              height: 72px;
            }
          }
        }
      }

      &__owner {
        .owner-info {
          display: flex;
          flex-wrap: wrap;
          align-items: center;
          gap: 16px;
          flex-direction: column;

          // background-color: $color_page_1;
          &__avt {
            width: 100px;
            height: 100px;
            border-radius: 50%;
            overflow: hidden;
            background: #eaeaea;

            .image {
              width: 100%;
              height: 100%;
              object-fit: cover;
            }
          }

          &__name {
            width: calc(100% - 78px);

            .name {
              font-size: 20px;
              font-weight: 600;
              text-align: center;
              color: $color_page_1;
              @include _line_clamp(1);
            }

            .profile {
              color: $color_page_1;

              &:hover {
                color: $color_page;
                text-decoration: underline;
              }
            }
          }
        }

        .owner-contact {
          display: flex;
          gap: 12px;
          align-items: center;
          flex-direction: column;

          &__item {
            padding: 12px 16px;
            width: 100%;
            border-radius: 4px;
            background-color: $color_page_1;
            color: #fff;
            display: flex;
            align-items: center;
            gap: 8px;

            &:hover {
              box-shadow: 2px 3px 6px 0px rgba($color_page_1, 0.5);
            }

            i {
              display: block;
              color: #fff;
              font-size: 18px;

              &::before {
                display: flex;
                justify-content: center;
                align-items: center;
                width: 100%;
                height: 100%;
              }
            }

            span {
              color: #fff;
              font-weight: 500;
              text-overflow: ellipsis;
              white-space: nowrap;
              overflow: hidden;
              width: 100%;
              display: block;
            }
          }
        }

        .owner-action {
          .button {
            width: 100%;
            background: $color_page_2;
            color: #fff;
            padding: 14px 26px;
            gap: 8px;

            i {
              font-size: 24px;
            }

            &:hover {
              box-shadow: 2px 3px 6px 0px rgba($color_page_2, 0.5);
            }
          }
        }
      }

      &__price {
        .box-header__title {
          font-size: 20px;
        }

        .item {
          padding: 12px 20px;

          &:nth-child(odd) {
            background: #f4f4f4;
          }

          &-content {
            color: $color_page_2;
            font-weight: 600;
            display: flex;
            align-items: center;
            justify-content: right;
            height: 100%;

            .unit {
              position: relative;
              top: -4px;
              margin-left: 4px;
              text-decoration: underline;
              font-size: 14px;
            }
          }
        }
      }

      &__report {
        padding: 16px 0 20px;
        text-align: right;

        a {
          color: #abaaaa;

          i {
            margin-right: 8px;
            color: #abaaaa;
          }

          &:hover {
            span {
              color: #00b7ff;
              text-decoration: underline;
            }
          }
        }
      }

      &__room {
        .box-header__desc {
          font-size: 14px;
        }
      }
    }

    .box-header {
      &__flex {
        display: flex;
        justify-content: space-between;
        gap: 20px;
      }

      &__inline {
        span {
          i {
            font-size: 22px;
            display: flex;
            align-items: center;
            justify-content: center;
          }

          &.verifi {
            color: $color_page;
          }

          &.trend {
            color: #ff5c00;
          }
        }

        .item {
          &-address {
            color: $color_page_1;
            font-size: 14px;
          }
        }
      }

      &__wrap {
        .box-tags {
          .inner {
            display: flex;
            align-items: center;
            gap: 8px;

            .item {
              color: $color_page_1;
              font-size: 14px;
              line-height: 1;
              padding: 10px 12px;
              border-radius: 6px;
              background: #ccdad6;

              &:hover {
                background: #9fbde7;
              }
            }
          }
        }

        .box-action {
          display: flex;
          align-items: center;
          justify-content: right;
          gap: 6px;

          .item {
            width: 36px !important;
            height: 36px !important;
            display: block;
            border-radius: 50%;
            border: 1px solid #827f7f;
            background: #fff;

            &-save,
            &-share {
              width: 100%;
              height: 100%;
              cursor: pointer;
              display: flex;
              align-items: center;
              justify-content: center;
            }

            svg {
              width: 20px !important;
              height: 20px !important;
            }

            i {
              width: 100%;
              height: 100%;
              font-size: 18px;
              color: #827f7f;
              display: flex;
              align-items: center;
              justify-content: center;
            }

            &-save {
              svg {
                cursor: pointer;
                fill: rgba(255, 255, 255, 0);
                stroke: #827f7f;
                @include _transition;
              }

              &.active {
                svg {
                  fill: #f30202;
                  stroke: #f30202;
                }
              }
            }
          }
        }
      }

      &__tag {
        display: flex;
        align-items: center;
        gap: 10px;
      }

      .box-address {
        margin: 20px 0;
      }
    }

    .box-gallery__wrap {
      display: flex;
      gap: 20px;

      .box-home__review {
        max-width: 330px;
        position: relative;

        img {
          position: absolute;
          inset: 0;
          width: 100%;
          height: 100%;
          background-color: #fff;
          z-index: 99;
        }
      }

      .box-gallery__home {
        width: calc(100% - 330px);
        grid-template-columns: repeat(3, 1fr);

        .gallery__item {
          &:nth-child(3) {
            position: relative;

            .gallery__item--overlay {
              position: absolute;
              top: 0;
              left: 0;
              width: 100%;
              height: 100%;
              background-color: rgba($color: #024834, $alpha: 0.6);
              display: flex;
              align-items: center;
              justify-content: center;

              .gallery__see-all {
                background-color: #fff;
                border-radius: 30px;
                display: flex;
                align-items: center;
                gap: 8px;
                padding: 8px 24px;
                font-weight: 500;
                color: $color_page_1;

                &.gallery__item--link {
                  height: auto;
                }

                &:hover {
                  color: $color_page_2;
                }
              }
            }
          }
        }
      }
    }

    .box-gallery__home {
      margin-bottom: 20px;
      display: grid;
      grid-template-columns: repeat(4, 1fr);
      gap: 20px;

      &.single {
        width: 100%;
        height: 100%;
        display: block;

        .gallery__item {
          border-radius: 12px;
          overflow: hidden;
          position: relative;

          .gallery__item--overlay {
            position: absolute;
            bottom: 16px;
            right: 16px;
          }

          a {
            img {
              aspect-ratio: 2.5/1;
            }
          }

          .gallery__see-all {
            background-color: #fff;
            border-radius: 30px;
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px 24px;
            font-weight: 500;

            &.gallery__item--link {
              height: auto;
            }

            &:hover {
              color: $color_page_2;
            }
          }
        }
      }

      .gallery__item {
        border-radius: 12px;
        overflow: hidden;

        &:first-child {
          grid-column: span 2;
          grid-row: span 2;
        }

        &:nth-child(5) {
          position: relative;

          .gallery__item--overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba($color: #024834, $alpha: 0.6);
            display: flex;
            align-items: center;
            justify-content: center;

            .gallery__see-all {
              background-color: #fff;
              border-radius: 30px;
              display: flex;
              align-items: center;
              gap: 8px;
              padding: 8px 24px;
              font-weight: 500;

              &.gallery__item--link {
                height: auto;
              }

              &:hover {
                color: $color_page_2;
              }
            }
          }
        }

        &--link {
          display: block;
          height: 100%;

          img {
            height: 100%;
            aspect-ratio: 4/3;
            display: block;
            width: 100%;
            object-fit: cover;
          }
        }
      }
    }

    .show-more {
      color: $color_page_1;
      font-weight: 400;

      &:hover {
        color: #00b7ff;
        text-decoration: underline;
      }
    }

    .box-home__title {
      margin-bottom: 18px;

      h2 {
        font-size: 18px;
        font-weight: 600;
      }
    }

    .box-home__review {
      background-color: #fff;
      border-radius: 8px;
      margin-bottom: 20px;

      &.fixed {
        position: fixed;
        top: 0;
      }

      blockquote {
        // min-width: auto !important;
        margin: 0 auto !important;
      }
    }

    .box-home__info {
      &.location__distance {
        .item {
          align-items: flex-start;

          .icon {
            i {
              position: relative;
              top: 5px;
            }
          }

          .content {
            p {
              color: #999;
              font-size: 13px;
            }
          }
        }
      }

      &--cont {
        .row {
          gap: 16px 0;
        }

        .item {
          display: flex;
          align-items: center;
          gap: 12px;

          i {
            display: flex;
            justify-content: center;
            width: 26px;
            font-weight: 600;
            font-size: 20px;
            color: $color_page_1;
          }
        }

        .btn.see-more {
          font-weight: 400;
          font-size: 15px;
          padding: 10px 24px;
          border-radius: 8px;
          border: 1px solid $color_text;
          display: inline-block;
          color: $color_text;

          &:hover {
            background-color: #f9f9f9;
          }
        }
      }
    }

    .popup__info {
      &--title {
        margin-bottom: 12px;

        h3 {
          font-size: 22px;
          font-weight: 500;
        }
      }

      &--container {
        max-height: 700px;
        overflow-y: auto;
      }

      .popup-inner {
        width: 780px;
      }

      .info__gr {
        &--head {
          padding: 16px 0;
          font-size: 18px;
        }

        &--items {
          li {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 16px 0;
            border-bottom: 1px solid #eeeeee9c;

            &:last-child {
              border-bottom: none;
            }

            i {
              color: $color_page_1;
              font-weight: 600;
              font-size: 18px;
            }
          }

          &.info__gr--location {
            li {
              justify-content: space-between;

              p.label {
                display: flex;
                align-items: center;
                gap: 12px;

                i {
                  color: $color_page_1;
                  font-size: 18px;
                }
              }

              p.value {
                min-width: 100px;
                padding-right: 12px;
              }
            }
          }
        }
      }
    }

    .box-rules__cont {
      background-color: #fff;
      border: 1px solid #eee;
      padding: 16px;
      border-radius: 8px;

      .rules__item {
        display: flex;
        gap: 16px;
        padding: 16px 0;
        border-bottom: 1px solid #eee;

        &:last-child {
          border-bottom: none;
        }

        .label {
          display: flex;
          // align-items: center;
          gap: 8px;
          width: 30%;

          i {
            width: 30px;
            font-size: 18px;
          }

          span {
            font-weight: 500;
          }
        }

        .value {
          width: calc(100% - 30% - 8px);

          p {
            white-space: pre-line;
          }
        }
      }
    }

    .box-home__room {
      .room__list {
        .item {
          background: #FFF;
          border-radius: 16px;
          border: 1px solid #eaeaea;
          padding: 16px;
          gap: 16px;

          .image {
            img {
              aspect-ratio: 1/1;
              object-fit: cover;
              display: block;
              width: 100%;
              height: 100%;
              border-radius: 8px;
            }
          }

          .link-detail {
            display: block;
            font-weight: 500;
            color: $color_page_1;
            font-size: 15px;
            padding-top: 10px;

            &:hover {
              text-decoration: underline;
            }
          }

          .props-wrap {
            display: flex;
            align-items: center;
            gap: 4px;

            .item-quantity {
              display: flex;
              justify-content: center;
              align-items: center;
              width: 24px;
              height: 24px;
              white-space: nowrap;
              background-color: #eaeaea;
              font-size: 10px;
              font-weight: 500;
              border-radius: 3px;
            }
          }

          .info {
            display: flex;
            flex-direction: column;
            gap: 4px;

            .text-head {
              margin-bottom: 0;
            }

            &-wrap {
              display: flex;
              justify-content: space-between;
            }

            &-detail {
              display: flex;
              gap: 4px;
            }



            sup {
              font-size: 12px;
              position: relative;
              top: -4px;
            }
          }

          .item-text {
            font-weight: 400;
            color: #585555;
          }

          .text-head {
            text-transform: uppercase;
            color: #585555;
            font-size: 14px;
            margin-bottom: 6px;
            display: block;

            span.qnt {
              background-color: $color_page_2;
              padding: 4px 9px;
              color: #fff;
              font-weight: 500;
              font-size: 10px;
              border-radius: 20px;

              &.empty {
                background-color: #999;
              }
            }
          }

          .icon {
            width: 24px;
            height: 24px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
          }

          .price {
            .wrap {
              align-items: flex-start;
              gap: 8px 0;

              >* {
                line-height: 1;
              }
            }
          }

          .guest-wrap {
            display: flex;
            flex-direction: column;
            gap: 8px;

            >* {
              display: flex;
              align-items: flex-end;
              gap: 3px;
            }
          }

          .guest-number {
            font-weight: 500;
          }

          .voucher {
            .voucher-code {
              position: relative;

              i {
                width: 16px;
                font-size: 13px;
                position: relative;
                height: 100%;
                display: inline-flex;
                justify-content: center;
                align-items: center;
                width: 20px;
                position: relative;
                top: 1px;
                padding-left: 2px;

                &::after {
                  content: '';
                  position: absolute;
                  top: -2px;
                  right: 0;
                  width: 1px;
                  height: calc(100% + 7px);
                  background-color: #fff;
                }
              }

              background-color: $color_page;
              font-size: 10px;
              color: #fff;
              font-weight: 500;
              padding: 4px 8px 4px 0px;
              border-radius: 4px;

              &::before,
              &::after {
                content: '';
                position: absolute;
                top: 50%;
                left: 0;
                width: 7px;
                height: 7px;
                background-color: #fff;
                transform: translate(-50%, -50%);
                border-radius: 50%;
              }

              &::after {
                left: 100%;
              }
            }
          }

          .price-old {
            font-size: 13px;
            color: #999;
            text-decoration: line-through;
            line-height: 1;
          }

          .price__number {
            font-weight: 600;
            font-size: 18px;
            color: $color_page_2;
          }

          .include-break {
            font-size: 11px;
            color: #2e2a2a;
          }

          h3 {
            font-size: 16px;
            font-weight: 600;
            text-transform: capitalize;
          }

          .item-qty {
            display: flex;
            align-items: center;
          }

          .wrap {
            display: flex;
            align-items: flex-end;
            flex-direction: column;
            gap: 20px;

            .input-number {
              &.error {
                border: 1px solid red;
              }
            }

            .btn-booking {
              height: 40px;
              background-color: $color_page_3;
              line-height: 1;
              border: 1px solid darken($color_page_3, 5%);

              &.disabled {
                background-color: #999;
                border-color: #999;
                pointer-events: none;
                cursor: not-allowed;
              }

              &:hover {
                background-color: lighten($color_page_3, 5%);
              }
            }
          }
        }
      }
    }

    .box-form__booking {
      display: flex;
      width: fit-content;
      gap: 4px;
      margin-bottom: 20px;
      padding: 8px;
      background-color: $color_page_2;
      border-radius: 4px;
      position: relative;
      width: 100%;

      .form-group {
        margin-bottom: 0;
      }

      >div.form-group {
        width: 100%;
        min-width: 300px;
      }

      .form-control {
        background-color: #fff;
        // border: 1px solid $color_page_1;
      }

      .dropdown {

        // max-width: 300px;
        &.rental-type {
          .dropdown-item {
            &:hover {
              background-color: #eaecf0;
            }
          }
        }

        &-select {
          background-color: #fff;
          // border: 1px solid $color_page_1;
          border-radius: 4px;
        }

        &-title {
          display: flex;
          align-items: center;

          i {
            font-size: 18px;
            color: $color_page_1;
            margin-right: 10px;
          }

          .dot {
            display: block;
            margin: 0 10px;
            width: 6px;
            height: 6px;
            border-radius: 50%;
            background-color: $color_page_1;
          }
        }

        &-value {
          display: flex;
          align-items: center;
        }

        &-list {
          box-shadow: rgba(99, 99, 99, 0.2) 0px 2px 8px 0px;
        }

        span {
          font-weight: 400;
        }

        #booking-adult,
        #booking-child {
          font-weight: 500;
        }

        .dropdown-item {
          display: flex;
          align-items: center;
          justify-content: space-between;

          &:hover {
            background-color: transparent;
            color: $color_text;
          }

          &.dropdown-children {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;

            .select-age {
              width: calc(50% - 4px);
            }
          }

          .item-qty {
            display: inline-flex;
            gap: 4px;

            input {
              width: 50px;
              padding: 10px;
              background-color: #fff;
              border: 1px solid #eee;
            }

            .variation {
              background-color: #fff;
              border: 1px solid #eee;

              &:hover {
                background-color: $color_page_1;
              }
            }
          }
        }
      }

      .action {
        padding: 0;
      }

      .btn.primary {
        height: 46px;
        font-size: 14px;
        background-color: $color_page;
        border: 1px solid $color_page;
        font-weight: 500;
        white-space: nowrap;
      }
    }

    .box-home__whole {
      .item {
        background: #FFF;
        border-radius: 16px;
        border: 1px solid #eaeaea;
        padding: 16px;
        gap: 16px;

        .note {
          padding: 4px 8px;
          background-color: #fff;
          border: 1px solid red;
          border-radius: 4px;
          display: inline-flex;
          align-items: center;
          gap: 4px;
          font-size: 12px;
          color: red;
          font-weight: 500;
          margin: 4px 0;
        }

        .image {
          img {
            aspect-ratio: 1/1;
            object-fit: cover;
            display: block;
            width: 100%;
            height: 100%;
            border-radius: 8px;
          }
        }

        .link-detail {
          display: block;
          font-weight: 500;
          color: $color_page_1;
          font-size: 15px;
          padding-top: 10px;

          &:hover {
            text-decoration: underline;
          }
        }

        .props-wrap {
          display: flex;
          align-items: center;
          gap: 4px;

          .item-quantity {
            display: flex;
            justify-content: center;
            align-items: center;
            width: 24px;
            height: 24px;
            white-space: nowrap;
            background-color: #eaeaea;
            font-size: 10px;
            font-weight: 500;
            border-radius: 3px;
          }
        }

        .room-wrap {
          padding-bottom: 10px;
          border-bottom: 1px solid #eaeaea;
          margin-bottom: 10px;

          &:last-child {
            border-bottom: none;
            margin-bottom: 0;
            padding-bottom: 0;
          }
        }

        .info {
          display: flex;
          flex-direction: column;
          gap: 4px;

          &-wrap {
            display: flex;
            justify-content: space-between;
          }

          &-detail {
            display: flex;
            // flex-direction: column;
            gap: 4px;
          }



          sup {
            font-size: 12px;
            position: relative;
            top: -4px;
          }
        }

        .item-text {
          font-weight: 400;
          color: #585555;
        }

        .text-head {
          text-transform: uppercase;
          color: #585555;
          font-size: 14px;
          margin-bottom: 6px;
          display: block;
        }

        .icon {
          width: 24px;
          height: 24px;
          display: inline-flex;
          align-items: center;
          justify-content: center;
        }

        .price {
          .wrap {
            align-items: flex-start;
            gap: 8px 0;

            >* {
              line-height: 1;
            }
          }
        }

        .guest-wrap {
          display: flex;
          flex-direction: column;
          gap: 8px;

          >* {
            display: flex;
            align-items: flex-end;
            gap: 3px;
          }
        }

        .guest-number {
          font-weight: 500;
        }

        .voucher {
          .voucher-code {
            position: relative;

            i {
              width: 16px;
              font-size: 13px;
              position: relative;
              height: 100%;
              display: inline-flex;
              justify-content: center;
              align-items: center;
              width: 20px;
              position: relative;
              top: 1px;
              padding-left: 2px;

              &::after {
                content: '';
                position: absolute;
                top: -2px;
                right: 0;
                width: 1px;
                height: calc(100% + 7px);
                background-color: #fff;
              }
            }

            background-color: $color_page;
            font-size: 10px;
            color: #fff;
            font-weight: 500;
            padding: 4px 8px 4px 0px;
            border-radius: 4px;

            &::before,
            &::after {
              content: '';
              position: absolute;
              top: 50%;
              left: 0;
              width: 7px;
              height: 7px;
              background-color: #fff;
              transform: translate(-50%, -50%);
              border-radius: 50%;
            }

            &::after {
              left: 100%;
            }
          }
        }

        .price-old {
          font-size: 13px;
          color: #999;
          text-decoration: line-through;
          line-height: 1;
        }

        .price__number {
          font-weight: 600;
          font-size: 18px;
          color: $color_page_2;
        }

        .include-break {
          font-size: 11px;
          color: #2e2a2a;
        }

        h3 {
          font-size: 16px;
          font-weight: 600;
          text-transform: capitalize;
        }

        .item-qty {
          display: flex;
          align-items: center;
        }

        .wrap {
          display: flex;
          align-items: flex-end;
          flex-direction: column;
          gap: 20px;

          .btn-booking {
            height: 40px;
            background-color: $color_page_3;
            line-height: 1;
            border: 1px solid darken($color_page_3, 5%);

            &:hover {
              background-color: lighten($color_page_3, 5%);
            }
          }
        }
      }

      .btn-booking {
        height: 40px;
        background-color: $color_page_3;
        line-height: 1;
        border: 1px solid darken($color_page_3, 5%);

        &.disabled {
          background-color: #999;
          border-color: #999;
          color: #fff;
          pointer-events: none;
          cursor: not-allowed;
        }

        &:hover {
          background-color: lighten($color_page_3, 5%);
        }
      }
    }

    .box-home__props {
      .item {
        display: flex;
        gap: 4px;

        .item-icon {
          width: 24px;
          height: 24px;
          display: flex;
          justify-content: center;
          align-items: center;
          font-weight: 500;
        }

        &.button {
          cursor: pointer;
          padding: 6px 16px;
          border-radius: 4px;
          border: 1px solid $color_page_1;
          display: flex;
          justify-content: center;
          align-items: center;

          &:hover {
            color: $color_page_2;
          }
        }
      }

    }

    .box-home__villa {
      .villa__list {
        .item {
          box-shadow: 0 0 4px 0 rgba(0, 0, 0, 0.1);
          padding: 12px;
          border-radius: 8px;

          &-title {
            font-size: 18px;
            font-weight: 700;
            margin-bottom: 12px;
            color: $color_page_1;
            text-transform: capitalize;
          }

          .image+.info-list {
            padding-top: 12px;
            display: flex;
            flex-wrap: wrap;
            gap: 4px 10px;

            li {
              &:last-child {
                margin-bottom: 0;
              }

              i {
                color: $color_page_1;
                margin-right: 2px;
                width: 24px;
                display: inline-flex;
                justify-content: center;
                align-items: center;
                font-weight: 600;
              }

              span {
                font-weight: 300;
              }
            }
          }

          .btn-villa-detail {
            color: $color_page_1;
            background-color: transparent;
            padding: 0;
            margin-top: 12px;
            border: 0;

            &:hover {
              color: $color_page_2;
            }
          }

          .image {
            a {
              display: block;
            }

            img {
              aspect-ratio: 4/3;
              object-fit: cover;
              border-radius: 8px;
              display: block;
            }
          }

          table {
            width: 100%;
            margin-bottom: 12px;

            thead {
              tr {
                border-radius: 8px 8px 0 0;

                th {
                  font-weight: 600;
                  color: $color_text;
                  border-bottom: 1px solid #f2f3f3;
                  background-color: #f7f9fa;
                  padding: 10px 12px;
                  border-right: 1px solid #f2f3f3;

                  &:first-child {
                    border-top-left-radius: 8px;
                    border-left: none;
                  }

                  &:last-child {
                    border-top-right-radius: 8px;
                    border-right: none;
                  }
                }
              }
            }

            tbody {
              tr {
                margin-bottom: 10px;

                td {
                  padding: 10px 12px;
                  border-bottom: 1px solid #f2f3f3;
                  font-size: 14px;
                  font-weight: 400;
                  vertical-align: middle;

                  strong.price {
                    font-weight: 600;
                    color: $color_page_2;
                  }

                  .btn-room-detail {
                    padding: 0;
                  }

                  a.btn-booking {
                    padding: 6px 16px;
                    background-color: $color_page_2;
                    border: 1px solid $color_page_2;
                    font-weight: 500;
                    font-size: 14px;
                    margin: 0 auto;
                  }

                  .info-list {
                    display: flex;
                    flex-wrap: wrap;
                    gap: 0 6px;

                    li {
                      width: 100%;
                      margin-bottom: 10px;

                      &:last-child {
                        margin-bottom: 0;
                      }

                      i {
                        color: $color_page_1;
                        margin-right: 2px;
                        width: 24px;
                      }

                      span.dot {
                        margin: 0 4px;
                        background-color: #999;
                        width: 6px;
                        height: 6px;
                        border-radius: 50%;
                        display: inline-block;
                      }
                    }
                  }

                  .item-qty {
                    display: flex;
                  }
                }
              }
            }
          }

          .action {
            padding-top: 10px;
            display: flex;
            justify-content: space-between;

            .price {
              color: $color_page_2;
              font-weight: 600;
              font-size: 22px;
            }

            .btn-booking {
              padding: 6px 16px;
              background-color: $color_page_2;
              border: 1px solid $color_page_2;
              font-weight: 500;
              font-size: 14px;
            }
          }

          .btn-room-detail {
            background-color: transparent;
            color: $color_page;
            border: none;
            padding: 10px 0;
            font-weight: 400;
            font-size: 14px;
          }
        }
      }
    }

    .box-hosting-distance {
      ul {
        padding: 8px 0;
      }

      .item {
        font-size: 14px;

        .item-title {
          display: flex;
          align-items: center;
          gap: 10px;
        }

        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 6px 0;

        p {
          color: $color_page_1;
        }
      }

      .see-more {
        color: $color_page_1;
      }
    }

    .nav__fixed {
      border: 1px solid #EAEAEA;
      border-radius: 8px;
      background-color: #fff;
      margin-bottom: 20px;

      &.active {
        position: fixed;
        top: 68px;
        left: 0;
        z-index: 100;
        width: 100%;
        box-shadow: 0 0 4px 0 rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
        border-radius: 0;

        .nav__fixed--right {
          visibility: visible;
        }
      }

      .menu__list {
        display: flex;

        li {
          a {
            display: block;
            padding: 20px 28px;
            font-weight: 500;
            color: $color_text;

            &:hover {
              color: $color_page;
            }
          }
        }
      }

      &--right {
        display: flex;
        align-items: center;
        justify-content: flex-end;
        gap: 16px;
        height: 100%;
        visibility: hidden;
      }

      &--price {
        text-align: right;

        &--title {
          font-size: 12px;
          color: #999;
        }

        &--main {
          font-size: 18px;
          color: $color_page_2;
          font-weight: 600;
        }
      }

      &--action {
        .button {
          background-color: $color_page_3;
          color: #fff;
          border: 1px solid darken($color_page_3, 5%);
          height: 40px;
          padding: 10px 20px;

          &:hover {
            background-color: lighten($color_page_3, 5%);
            box-shadow: 0 0 4px 0 rgba($color_page_3, 0.5);
          }
        }
      }
    }

    .popup__room--detail {
      .popup-inner {
        width: 700px;

        &__wrap {
          padding: 10px;

          .room__gallery--single,
          .room__gallery--cont {
            .item {
              margin-bottom: 10px;

              img {
                display: block;
                aspect-ratio: 16/9;
                object-fit: cover;
                border-radius: 10px;
                width: 100%;
              }
            }
          }

          .room__gallery--nav {
            margin: 0 -5px;

            .item {
              margin: 0 5px;

              img {
                display: block;
                aspect-ratio: 16/9;
                object-fit: cover;
                border-radius: 10px;
              }
            }
          }

          .room-detail__info {
            margin-top: 16px;

            .room__info--title {
              h2 {
                font-size: 22px;
                font-weight: 600;
              }

              margin-bottom: 16px;
            }

            .room__info--desc {
              display: flex;
              align-items: center;
              gap: 10px;
              font-weight: 500;
              color: #555;

              span.dot {
                width: 6px;
                height: 6px;
                border-radius: 50%;
                background-color: $color_text_1;
              }
            }

            .room__info--wrap {
              margin-top: 20px;

              .heading {
                font-size: 18px;
                font-weight: 500;
                display: flex;
                align-items: center;
                gap: 16px;
                margin-bottom: 16px;

                &::after {
                  content: "";
                  background-color: #eaeaea;
                  flex: 1;
                  height: 1px;
                }
              }

              .bed__wrap {
                display: flex;
                flex-wrap: wrap;
                margin: 0 -10px;

                .item {
                  width: calc(100% / 2 - 20px);
                  margin: 0 10px;
                  display: flex;
                  align-items: center;
                  gap: 16px;

                  .qnt {
                    font-weight: 600;
                  }

                  .content {
                    display: flex;
                    flex-direction: column;

                    .name {
                      font-weight: 600;
                    }
                  }
                }
              }

              .room-properties {
                .item {
                  display: flex;
                  align-items: center;
                  gap: 12px;

                  i {
                    display: flex;
                    justify-content: center;
                    width: 26px;
                    font-size: 20px;
                    color: #024834;
                  }
                }
              }
            }
          }
        }
      }
    }

    .popup__villa--detail {
      .popup-inner {
        width: 700px;

        &__wrap {
          padding: 10px;

          .villa__gallery--single,
          .villa__gallery--cont {
            .item {
              margin-bottom: 10px;

              img {
                display: block;
                aspect-ratio: 16/9;
                object-fit: cover;
                border-radius: 10px;
                width: 100%;
              }
            }
          }

          .villa__gallery--nav {
            margin: 0 -5px;

            .item {
              margin: 0 5px;

              img {
                display: block;
                aspect-ratio: 16/9;
                object-fit: cover;
                border-radius: 10px;
              }
            }
          }

          .villa-detail__info {
            margin-top: 16px;

            .villa__info--title {
              h2 {
                font-size: 22px;
                font-weight: 600;
              }

              margin-bottom: 16px;
            }

            .villa__info--desc {
              display: flex;
              align-items: center;
              gap: 10px;
              font-weight: 500;
              color: #555;

              span.dot {
                width: 6px;
                height: 6px;
                border-radius: 50%;
                background-color: $color_text_1;
              }
            }

            .villa__info--wrap {
              margin-top: 20px;

              .heading {
                font-size: 18px;
                font-weight: 500;
                display: flex;
                align-items: center;
                gap: 16px;
                margin-bottom: 16px;

                &::after {
                  content: "";
                  background-color: #eaeaea;
                  flex: 1;
                  height: 1px;
                }
              }

              .villa-properties {
                .item {
                  display: flex;
                  align-items: center;
                  gap: 12px;

                  i {
                    display: flex;
                    justify-content: center;
                    width: 26px;
                    font-size: 20px;
                    color: #024834;
                  }
                }
              }
            }
          }
        }
      }
    }


    .no-room-state {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 2rem;
      text-align: center;
      gap: 1rem;

      .icon-container {
        i {
          font-size: 48px;
          color: $color_page_1;
        }
      }

      .content {
        display: flex;
        flex-direction: column;
        gap: 0.5rem;

        .title {
          font-size: 1.25rem;
          font-weight: 500;
          color: $color_page_1;
        }

        .subtitle {
          font-size: 0.875rem; // text-sm
          color: #6b7280; // màu xám cho text-muted-foreground
        }
      }

      .change-search-button {
        background-color: $color_page_1;
        color: #ffffff;
        border: none;
        padding: 0.5rem 1rem;
        border-radius: 0.375rem;
        cursor: pointer;
        transition: background-color 0.3s;

        &:hover {
          background-color: $color_page_1;
        }
      }
    }

  }

  &-item {
    background-color: #fff;
    height: 100%;
    overflow: hidden;
    @include _transition;

    &__img {
      position: relative;
      border-radius: 8px;

      .btn-review {
        position: absolute;
        bottom: 10px;
        left: 10px;
        width: 90px;
        height: 30px;
        border-radius: 30px;
        border: none;
        background-color: rgb(255, 255, 255);
        overflow: hidden;
        box-shadow: 10px 10px 10px rgba(0, 0, 0, 0.062);
        padding: 4px;

        &__frame {
          position: relative;
          display: flex;
          align-items: center;
          justify-content: space-between;
          gap: 4px;
          transition-duration: 0.3s;
          overflow: hidden;
          border-radius: 30px;

          &::before {
            content: "";
            position: absolute;
            left: 0;
            top: 0;
            width: 22px;
            height: 100%;
            background-color: $color_page;
            border-radius: 50%;
            overflow: hidden;
            z-index: 2;
            transition-duration: 0.3s;
          }
        }

        .IconContainer {
          display: flex;
          align-items: center;
          position: relative;
          z-index: 2;

          i {
            position: relative;
            right: -1px;
            width: 22px;
            height: 100%;
            color: #fff;
            font-size: 12px;

            &::before {
              width: 100%;
              height: 22px;
              display: flex;
              justify-content: center;
              align-items: center;
            }
          }
        }

        .text {
          position: relative;
          z-index: 2;
          height: 100%;
          width: calc(100% - 30px);
          color: rgb(26, 26, 26);
          transition-duration: 0.3s;
          font-size: 1.04em;
          font-weight: 600;
          font-size: 12px;
        }

        &.bookmarkBtn:hover {
          .btn-review__frame {
            &::before {
              width: 100%;
              border-radius: 30px;
            }

            .text {
              color: #fff;
            }
          }
        }
      }

      .box-image {
        display: block;
        background: #d5ffbb39;
        position: relative;
        border-radius: 8px;

        img {
          display: block;
          width: 100%;
          aspect-ratio: 1/1;
          object-fit: cover;
          border-radius: 8px;

          &::after {
            content: "";
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba($color: #000000, $alpha: 0.1);
            border-radius: 8px;
            display: block;
          }
        }
      }
    }

    &__content {
      padding: 12px 0;
      display: flex;
      flex-direction: column;
      gap: 10px 0;
      border-bottom-right-radius: 8px;
      border-bottom-left-radius: 8px;

      &--head {
        display: flex;
        align-items: flex-start;
        justify-content: space-between;
        gap: 0 4px;
      }

      &--title {
        width: 100%;

        h3 {
          font-size: 16px;
          font-weight: 500;
          text-overflow: ellipsis;
          color: $color_text;
          @include _transition;
          overflow: hidden;
          white-space: nowrap;
          max-width: calc(100% - 20px);
        }
      }

      &--link {
        display: flex;
        align-items: center;
        gap: 2px;
        flex-wrap: nowrap;
        width: 100%;

        i {
          color: $color_page;
          font-size: 16px;
        }
      }

      &--tag {
        position: absolute;
        top: 10px;
        left: 10px;

        i {
          background: #fff;
          border-radius: 100%;
          height: 30px;
          width: 30px;
          font-size: 18px;
          color: #ff5c00;
          display: flex;
          align-items: center;
          justify-content: center;
        }
      }

      &--save {
        position: absolute;
        bottom: 10px;
        right: 10px;
        width: 30px;
        height: 30px;

        svg {
          cursor: pointer;
          fill: rgba(255, 255, 255, 0);
          stroke: #fff;
          @include _transition;
        }

        &.active {
          svg {
            fill: #f30202;
            stroke: #f30202;
          }
        }
      }

      &--price {
        .price {
          display: flex;
          align-items: center;
          gap: 4px;

          &.price-old {
            .value {
              text-decoration: line-through;
              font-size: 13px;
              font-weight: 300;
              color: #999;
            }
          }
        }

        .voucher-code {
          position: relative;
          display: inline-flex;
          font-size: 10px;

          i {
            width: 16px;
            font-size: 13px;
            position: relative;
            height: 100%;
            display: inline-flex;
            justify-content: center;
            align-items: center;
            width: 20px;
            position: relative;
            top: 1px;
            padding-left: 2px;
            margin-right: 2px;

            &::after {
              content: '';
              position: absolute;
              top: -2px;
              right: 0;
              width: 1px;
              height: calc(100% + 7px);
              background-color: #fff;
            }
          }

          background-color: $color_page;
          font-size: 10px;
          color: #fff;
          font-weight: 500;
          padding: 2px 8px 2px 0px;
          border-radius: 4px;

          &::before,
          &::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 0;
            width: 7px;
            height: 7px;
            background-color: #fff;
            transform: translate(-50%, -50%);
            border-radius: 50%;
          }

          &::after {
            left: 100%;
          }
        }

        .value {
          font-size: 16px;
          color: $color_text;
          font-weight: 600;
          text-overflow: ellipsis;
          white-space: nowrap;
          overflow: hidden;
          width: 100%;
          display: block;
          color: $color_page_2;

          small {
            color: $color_text_1;
            font-weight: 300;
          }
        }
      }

      &--address {
        display: flex;
        align-items: center;
        gap: 4px;

        p,
        i {
          color: #999;
        }

        p {
          font-size: 13px;
          font-weight: 300;
          color: $color_text_1;
          @include _line_clamp(1);
        }

        i {
          font-size: 13px;
          transform: translateY(-1px);
        }
      }
    }

    &__props {
      display: flex;
      align-items: center;
      gap: 8px;
      width: 100%;

      li {
        cursor: pointer;

        i {
          display: flex;
          justify-content: center;
          width: 24px;
          font-size: 14px;
          color: rgba($color_page_1, $alpha: 0.8);
          @include _transition;
        }

        &:hover {
          i {
            color: $color_page_1;
          }
        }
      }
    }



    &--row {
      display: flex;

      .home-item__img {
        width: calc(100% * 5 / 12);

        .box-image {
          img {
            aspect-ratio: 16/10;
          }
        }
      }

      .home-item__content {
        width: calc(100% * 7 / 12);
        padding: 20px;
        gap: 16px;

        h3 {
          height: auto;
          @include _line_clamp(2);
        }
      }
    }

    &:hover {

      // box-shadow: 3px 6px 12px 0px rgba(52, 61, 55, 0.12);
      .home-item__content--title h3 {
        color: $color_page;
      }
    }
  }

  &-videos {
    .review {
      &-item {
        display: flex;
        flex-wrap: wrap;
        gap: 20px;

        &__video {
          width: calc(50% - 10px);

          &--thumb {
            .image {
              aspect-ratio: 16/10;
            }
          }
        }

        &__content {
          width: calc(50% - 10px);
          padding-left: 0;

          &--inline {
            display: none;
          }

          &--desc {
            height: 72px;
            @include _line_clamp(3);
          }
        }
      }
    }
  }

  &__add {

    // margin: 40px 0;
    .grid {
      &.wide {
        max-width: 1200px !important;
      }
    }

    .box-header {
      margin-bottom: 20px;
    }

    .form__container {
      .action {
        display: flex;
        align-items: center;
        justify-content: flex-end;
        gap: 12px;
      }

      &--title {
        text-transform: uppercase;
        font-size: 20px;
        margin-bottom: 16px;
        font-weight: 700;
        color: $color_page_1;
      }

      .group_title {
        margin-bottom: 16px;
        font-size: 18px;
        font-weight: 600;
      }

      &--room {
        padding: 16px;
        background-color: #fff;
        border: 1px solid #ccc;

        .form__container--title {
          font-size: 18px;
          text-transform: uppercase;
          margin-bottom: 0;
        }

        .line {
          padding: 16px 0;
        }
      }

      .image__input {
        height: 100%;

        .box-upload__file {
          height: calc(100% - 16px);
          border-radius: 6px;
          border: 2px solid $color_page_1;
          overflow: hidden;

          .btn-upload__file {
            height: 100%;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            gap: 12px;
            padding: 16px;
          }

          p.title {
            text-transform: uppercase;
            color: $color_page_1;
            font-weight: 600;
            font-size: 18px;
          }

          .btn-uploader {
            position: relative;
            padding: 10px 20px;
            background-color: $color_page;
            border-radius: 20px;
            color: #fff;

            input {
              position: absolute;
              inset: 0;
              z-index: 1;
              opacity: 0;
              width: 100%;
              height: inherit;
              cursor: pointer;
              font-size: 0;
              padding: 0;
            }
          }

          .upload__img-wrap {
            height: 100%;
            width: 100%;
            display: none;

            .upload__img-box {
              width: 100%;
              height: 100%;
              position: relative;

              .thumb-img {
                width: 100%;
                height: 100%;
                background-position: center;
                background-size: cover;
                background-repeat: no-repeat;
              }

              .img-info {
                position: absolute;
                bottom: 0;
                left: 0;
                width: 100%;
                background-color: #fff;
                color: $color_page_1;
                padding: 6px 35px 6px 12px;

                span {
                  display: block;
                  white-space: nowrap;
                  text-overflow: ellipsis;
                  overflow: hidden;
                }
              }

              .upload__img-close {
                position: absolute;
                bottom: 0;
                right: 0;
                width: 35px;
                height: 35px;
                background: $color_page;
                display: flex;
                align-items: center;
                justify-content: center;
                cursor: pointer;

                i {
                  font-size: 14px;
                  color: #fff;
                }
              }
            }
          }
        }
      }

      .btn-remove {
        width: auto;
        display: inline-block;
        border: 1px solid $color_page_1;
        border-radius: 6px;
        padding: 6px 12px;
        color: $color_page_1;
        font-size: 14px;

        &:hover {
          background: $color_page_1;
          color: #fff;
        }
      }
    }
  }

  .box-search {
    background: $color_page_1;
    margin-bottom: 0;

    .search-input {
      i {
        top: 4px;
        height: 60px;
        width: 60px;
      }

      input {
        height: 54px;
        background-color: transparent;
        border-color: #fff;
        color: #fff;

        &::placeholder {
          color: rgba($color: #fff, $alpha: 0.8);
        }
      }
    }

    .search-list {
      .item {
        &:hover {
          background: #1f5b4a;
        }

        &.active {
          background-color: transparent;
        }

        &-title {
          color: #fff;
        }

        &-desc {
          color: rgba($color: #fff, $alpha: 0.6);
        }
      }

      .line {
        background-color: rgba($color: #fff, $alpha: 0.3);

        &::after {
          display: none;
        }
      }
    }

    &__wrap {
      height: 70px;
      background: transparent;
      border-radius: 0;
    }
  }

  .filter__cont {
    .close {
      display: none;
    }
  }

  .sidebar {
    &-header {
      gap: 10px;
      display: flex;
      flex-wrap: wrap;
      align-items: center;
      justify-content: space-between;
      padding-bottom: 20px;
      border-bottom: 1px solid #eaecf0;

      .title {
        font-size: 18px;
        font-weight: 700;
        color: $color_page_1;
      }

      .clearFilter {
        font-weight: 400;
        line-height: 1;

        &:hover {
          color: $color_page_2;
        }
      }
    }

    .line {
      padding: 16px 0;
    }

    &-frame {
      &__header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        gap: 8px;

        .title {
          font-size: 18px;
          font-weight: 700;
        }

        .icon {
          cursor: pointer;
          width: 32px;
          height: 32px;
          display: flex;
          align-items: center;
          justify-content: center;
          border-radius: 50%;

          i {
            font-size: 14px;
          }

          &:hover {
            background: #f4f4f4;
          }
        }
      }

      &__content {
        margin-top: 16px;

        &--qty {
          border: 1px solid #eaeaea;
          padding: 10px;
          border-radius: 6px;

          .item-qty {
            display: flex;
            align-items: center;
            justify-content: space-between;
            gap: 10px;
          }
        }

        &--action {
          margin-top: 20px;

          .view-more {
            font-weight: 400;
          }
        }

        .location-search {
          display: flex;
          align-items: center;
          gap: 0 12px;
          padding: 10px;
          border: 1px solid #eee;
          border-radius: 5px;
          position: relative;
          cursor: pointer;
          border-color: #000;

          &__icon {
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            background-color: rgba($color_page, $alpha: 0.4);

            i {
              font-size: 16px;
              color: $color_page_1;
            }
          }

          &__wrap {
            h5 {
              font-style: 14px;
              font-weight: 600;
            }

            p {
              font-size: 13px;
              color: #888;
            }
          }

          .key__location {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: #fff;
            display: none;
            font-size: 16px;
            font-weight: 500;
            padding: 12px;

            &.active {
              display: block;
            }
          }

          &__result {
            display: none;
            position: absolute;
            top: calc(100% + 1px);
            left: 0;
            width: 100%;
            padding: 10px;
            background-color: #fff;
            z-index: 9;
            border: 1px solid #eee;
            box-shadow: 2px 3px 6px 0px rgba($color_text, 0.1);
            border-radius: 4px;

            &.active {
              display: block;
            }

            .subitem {
              border-bottom: 1px solid #f1f1f1;

              &:last-child {
                border-bottom: none;
              }
            }

            .subitem-frame {
              padding: 12px;
              display: flex;
              align-items: center;
              gap: 12px;
              color: $color_text;

              &:hover {
                color: $color_page;
              }

              &__content {
                &--title {
                  font-weight: 500;
                }

                &--desc {
                  display: none;
                }
              }
            }
          }
        }

        .location-distance {
          margin-top: 16px;
        }

        .nav-checkbox {
          &.rating {
            .list-checkbox {
              i {
                &.fa-gold {
                  color: #ffb800;
                }
              }
            }
          }

          .list-checkbox {
            margin-bottom: 8px;

            &:last-child {
              margin-bottom: 0;
            }

            label {
              height: 26px;
              font-weight: 400;

              i {
                color: #eaeaea;
                font-size: 16px;
              }
            }
          }
        }

        .select2-container {
          width: 100% !important;
        }
      }

      &:last-child {
        margin-bottom: 0;
      }
    }
  }

  .main-content {
    height: 100%;

    .home__heading {
      i {
        font-size: 40px;
        color: #ff5c00;
      }
    }

    .btn-filter {
      display: none;

      a {
        display: flex;
        align-items: center;
        background-color: $color_page;
        padding: 8px 16px;
        color: #fff;
        line-height: 1;
        border-radius: 6px;
        gap: 4px;

        svg {
          width: 20px;
          height: 20px;

          path {
            fill: #fff;
          }
        }
      }
    }
  }

  .box-header {
    margin-bottom: 16px;

    &.heading {
      .box-header__title {
        font-size: 24px;
        text-transform: uppercase;
        color: $color_page_1;

        .highlight {
          color: $color_page_2;
        }
      }
    }
  }

  .box-custom {
    .inner {
      display: flex;
      align-items: center;
      justify-content: space-between;
      gap: 20px;
    }

    .arrange {
      &-wrap {
        display: flex;
        flex-wrap: wrap;
        align-items: center;
        gap: 8px;
      }

      &-label {
        font-size: 18px;
        font-weight: 800;
        color: #3a3c3e;
      }

      &-select {
        select {
          color: #827f7f;
          font-size: 16px;
          outline: none;
          border: none;
          cursor: pointer;
        }
      }
    }

    .layout {
      .btn-filter {
        display: none;
      }

      &-wrap {
        display: flex;
        align-items: center;
        gap: 8px;

        .item {
          &.active {
            svg {
              color: $color_page_1;
            }
          }
        }

        svg {
          color: #abaaaa;
          width: 20px;
          height: 18px;
          cursor: pointer;
        }
      }
    }
  }

  .box__no-result {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 80px 0;
    gap: 16px 0;

    .image {
      min-width: 200px;

      img {
        width: 100%;
        height: 100%;
        object-fit: contain;
        aspect-ratio: 1/1;
        display: block;
      }
    }

    .content {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      gap: 8px;

      h2 {
        text-transform: uppercase;
        color: $color_page_1;
        font-weight: 600;
      }

      p {
        color: #827f7f;
      }

      a {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 10px;
        padding: 8px 24px;
        background-color: $color_page;
        color: #fff;
        border-radius: 4px;
      }
    }
  }

  .bg-section {
    border: $borderFrame;
  }
}

// Customer Reviews Component - BEM SCSS
.customer-reviews {
  // max-width: 64rem;
  margin: 0 auto;
  // padding: 1.5rem;

  &__container {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
  }

  &__card {
    background: #fff;
    border-radius: 0.5rem;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  }

  &__header {
    padding: 1rem;
    border-bottom: 1px solid #e5e7eb;
  }

  &__title {
    font-size: 1.5rem;
    font-weight: 700;
    font-size: 24px;
    color: #111827;
    margin: 0;
  }

  &__content {
    padding: 1.5rem;
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
  }

  &__separator {
    height: 1px;
    background-color: #e5e7eb;
    margin: 0;
  }
}

// Rating Overview
.rating-overview {
  display: flex;
  flex-direction: column;
  gap: 1rem;

  @media (min-width: 640px) {
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
  }

  &__main {
    display: flex;
    align-items: center;
    gap: 1rem;
  }

  &__score {
    font-size: 2.25rem;
    font-weight: 700;
    color: #111827;
  }

  &__details {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
  }

  &__count {
    font-size: 0.875rem;
    color: #6b7280;
    margin: 0;
  }

  &__summary {
    text-align: left;

    @media (min-width: 640px) {
      text-align: right;
    }
  }

  &__label {
    font-size: 1.125rem;
    font-weight: 600;
    color: #111827;
    margin: 0;
  }

  &__description {
    font-size: 0.875rem;
    color: #6b7280;
    margin: 0;
  }
}

// Rating Stars
.rating-stars {
  display: flex;
  align-items: center;
  gap: 0.25rem;

  &--small {
    .rating-stars__star {
      width: 1rem;
      height: 1rem;
    }
  }

  &__star {
    width: 1rem;
    height: 1rem;
    fill: #d1d5db;
    color: #d1d5db;

    &--filled {
      fill: #fbbf24;
      color: #fbbf24;
    }

    &--partial {
      fill: url(#star-gradient);
      color: #fbbf24;
    }

    &--empty {
      fill: #d1d5db;
      color: #d1d5db;
    }
  }
}

// Rating Breakdown
.rating-breakdown {
  display: flex;
  flex-direction: column;
  gap: 1rem;

  &__title {
    font-size: 1.125rem;
    font-weight: 600;
    color: #111827;
    margin: 0;
  }

  &__list {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
  }
}

// Rating Metric
.rating-metric {
  display: flex;
  align-items: center;
  gap: 1rem;

  &__label {
    width: 5rem;
    font-size: 0.875rem;
    font-weight: 500;
    color: #374151;
  }

  &__progress {
    flex: 1;
  }

  &__bar {
    height: 0.5rem;
    background-color: #f3f4f6;
    border-radius: 9999px;
    overflow: hidden;
  }

  &__fill {
    height: 100%;
    background-color: $color_page;
    transition: width 0.3s ease;
  }

  &__score {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    width: 4rem;
    justify-content: flex-end;
  }

  &__star {
    width: 0.75rem;
    height: 0.75rem;
    fill: #fbbf24;
    color: #fbbf24;
  }

  &__value {
    font-size: 0.875rem;
    font-weight: 500;
    color: #111827;
  }
}

// Reviews Section
.reviews-section {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;

  &__title {
    font-size: 1.125rem;
    font-weight: 600;
    color: #111827;
    margin: 0;
  }
}

// Reviews List
.reviews-list {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

// Review Item
.review-item {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;

  &__layout {
    display: flex;
    align-items: flex-start;
    gap: 0.75rem;
  }

  &__content {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
  }

  &__header {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  &__user {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
  }

  &__name {
    font-weight: 600;
    color: #111827;
    margin: 0;
  }

  &__meta {
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }

  &__date {
    font-size: 0.875rem;
    color: #6b7280;
  }

  &__text {
    font-size: 0.875rem;
    line-height: 1.6;
    color: #6b7280;
    margin: 0;
  }

  &__avatar {
    display: flex;
    align-items: center;
    gap: 10px;

    &--info {
      display: flex;
      flex-direction: column;
      .review-item__traveler-type {
        font-size: 12px;
        color: #6b7280;
      }
    }
  }
}

// Review Avatar
.review-avatar {
  position: relative;
  width: 2.5rem;
  height: 2.5rem;
  flex-shrink: 0;

  &__image {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    object-fit: cover;
  }

  &__fallback {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    background-color: #f3f4f6;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.875rem;
    font-weight: 500;
    color: #6b7280;
    z-index: -1;
  }
}

// Review Images
.review-images {
  display: flex;
  gap: 0.5rem;
  margin-top: 0.75rem;

  &__item {
    width: 7.5rem;
    height: 7.5rem;
    border-radius: 0.5rem;
    object-fit: cover;
    border: 1px solid #e5e7eb;
  }
}

// Responsive Design
@media (max-width: 640px) {
  .customer-reviews {
    padding: 1rem;

    &__content {
      padding: 1rem;
    }
  }

  .rating-overview {
    &__main {
      flex-direction: column;
      align-items: flex-start;
      gap: 0.5rem;
    }

    &__summary {
      text-align: left;
    }
  }

  .rating-metric {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;

    &__label {
      width: auto;
    }

    &__score {
      width: auto;
      justify-content: flex-start;
    }
  }

  .review-item {
    &__layout {
      flex-direction: column;
    }

    &__header {
      flex-direction: column;
      align-items: flex-start;
      gap: 0.5rem;
    }
  }

  .review-images {
    &__item {
      width: 5rem;
      height: 5rem;
    }
  }
}