/* CSS cho phần đ<PERSON>h giá hosting */

.customer-reviews {
  margin: 2rem 0;
}

.customer-reviews__container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

.customer-reviews__card {
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  overflow: hidden;
}

.customer-reviews__header {
  padding: 1.5rem 2rem;
  border-bottom: 1px solid #f0f0f0;
}

.customer-reviews__content {
  padding: 2rem;
}

.customer-reviews__separator {
  height: 1px;
  background: #f0f0f0;
  margin: 2rem 0;
}

/* Rating Overview */
.rating-overview {
  display: flex;
  gap: 2rem;
  align-items: center;
  margin-bottom: 2rem;
}

.rating-overview__main {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.rating-overview__score {
  font-size: 3rem;
  font-weight: 700;
  color: #2c3e50;
  line-height: 1;
}

.rating-overview__details {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.rating-stars {
  display: flex;
  gap: 2px;
}

.rating-stars__star {
  width: 20px;
  height: 20px;
  fill: #e0e0e0;
}

.rating-stars__star--filled {
  fill: #FFD700;
}

.rating-stars__star--empty {
  fill: #e0e0e0;
}

.rating-stars--small .rating-stars__star {
  width: 16px;
  height: 16px;
}

.rating-overview__count {
  color: #666;
  font-size: 0.9rem;
  margin: 0;
}

.rating-overview__summary {
  margin-left: auto;
  text-align: right;
}

.rating-overview__label {
  font-size: 1.25rem;
  font-weight: 600;
  color: #2c3e50;
  margin: 0 0 0.25rem 0;
}

.rating-overview__description {
  color: #666;
  font-size: 0.9rem;
  margin: 0;
}

/* Rating Breakdown */
.rating-breakdown {
  margin: 2rem 0;
}

.rating-breakdown__title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #2c3e50;
  margin: 0 0 1.5rem 0;
}

.rating-breakdown__list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.rating-metric {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.rating-metric__label {
  min-width: 80px;
  font-weight: 500;
  color: #2c3e50;
}

.rating-metric__progress {
  flex: 1;
  height: 8px;
  background: #f0f0f0;
  border-radius: 4px;
  overflow: hidden;
}

.rating-metric__bar {
  width: 100%;
  height: 100%;
  position: relative;
}

.rating-metric__fill {
  height: 100%;
  background: linear-gradient(90deg, #FFD700, #FFA500);
  border-radius: 4px;
  transition: width 0.3s ease;
}

.rating-metric__score {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  min-width: 60px;
}

.rating-metric__star {
  width: 14px;
  height: 14px;
  fill: #FFD700;
}

.rating-metric__value {
  font-weight: 600;
  color: #2c3e50;
  font-size: 0.9rem;
}

/* Reviews Section */
.reviews-section {
  margin: 2rem 0;
}

.reviews-section__title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #2c3e50;
  margin: 0 0 1.5rem 0;
}

.reviews-list {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.review-item {
  padding: 1.5rem 0;
}

.review-item__layout {
  display: flex;
  gap: 1rem;
}

.review-avatar {
  flex-shrink: 0;
}

.review-avatar__image {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  object-fit: cover;
}

.review-avatar__fallback {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 600;
  font-size: 0.9rem;
}

.review-item__content {
  flex: 1;
}

.review-item__header {
  margin-bottom: 1rem;
}

.review-item__name {
  font-weight: 600;
  color: #2c3e50;
  margin: 0 0 0.5rem 0;
}

.review-item__meta {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.review-item__date {
  color: #666;
  font-size: 0.85rem;
}

.review-item__title {
  font-size: 1.1rem;
  font-weight: 600;
  color: #2c3e50;
  margin: 0 0 0.75rem 0;
}

.review-item__text {
  color: #444;
  line-height: 1.6;
  margin: 0 0 1rem 0;
}

.review-images {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
  margin: 1rem 0;
}

.review-images__item {
  width: 80px;
  height: 80px;
  border-radius: 8px;
  object-fit: cover;
  cursor: pointer;
  transition: transform 0.2s ease;
}

.review-images__item:hover {
  transform: scale(1.05);
}

.review-item__traveler-type {
  margin-top: 1rem;
  padding: 0.5rem 1rem;
  background: #f8f9fa;
  border-radius: 6px;
  display: inline-block;
}

.review-item__label {
  font-weight: 500;
  color: #666;
  margin-right: 0.5rem;
}

.review-item__value {
  color: #2c3e50;
  font-weight: 500;
}

/* Responsive */
@media (max-width: 768px) {
  .rating-overview {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .rating-overview__summary {
    margin-left: 0;
    text-align: left;
  }

  .rating-metric {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .rating-metric__label {
    min-width: auto;
  }

  .rating-metric__progress {
    width: 100%;
  }

  .review-item__layout {
    flex-direction: column;
    gap: 0.75rem;
  }

  .review-avatar {
    align-self: flex-start;
  }

  .customer-reviews__content {
    padding: 1rem;
  }
}
