<?php
/**
 * Test file để kiểm tra các fix đã thực hiện
 */

echo "=== TEST CÁC FIX ĐÃ THỰC HIỆN ===\n\n";

// 1. Test format số
echo "1. Test format số:\n";
$testNumbers = [4.567, 3.2, 0, null, 4.0];
foreach ($testNumbers as $num) {
    $formatted = number_format($num ?? 0, 1);
    echo "- {$num} → {$formatted}\n";
}
echo "\n";

// 2. Test tạo initials từ tên
echo "2. Test tạo initials từ tên:\n";
$testNames = [
    '<PERSON><PERSON><PERSON><PERSON>ăn <PERSON>',
    '<PERSON>r<PERSON><PERSON>',
    '<PERSON><PERSON> <PERSON>',
    '<PERSON><PERSON><PERSON> Thị <PERSON>hu <PERSON>ơ<PERSON>',
    '<PERSON>h<PERSON>ch hàng',
    ''
];

foreach ($testNames as $name) {
    $nameParts = explode(' ', trim($name));
    if (count($nameParts) > 1) {
        $initials = strtoupper(substr($nameParts[0], 0, 1)) . strtoupper(substr($nameParts[count($nameParts) - 1], 0, 1));
    } else {
        $initials = strtoupper(substr($name, 0, 1)) . (isset($name[1]) ? strtoupper($name[1]) : 'H');
    }
    echo "- '{$name}' → '{$initials}'\n";
}
echo "\n";

// 3. Test traveler type labels
echo "3. Test traveler type labels:\n";
$types = [
    'business'  => 'Công tác',
    'leisure'   => 'Du lịch',
    'family'    => 'Gia đình',
    'couple'    => 'Cặp đôi',
    'friends'   => 'Bạn bè',
    'solo'      => 'Một mình'
];

foreach ($types as $key => $label) {
    echo "- '{$key}' → '{$label}'\n";
}
echo "\n";

// 4. Test rating labels
echo "4. Test rating labels:\n";
$ratings = [5.0, 4.7, 4.2, 3.8, 3.2, 2.5, 1.0, 0];
foreach ($ratings as $rating) {
    if ($rating >= 4.5) {
        $label = 'Xuất sắc';
    } elseif ($rating >= 3.5) {
        $label = 'Tốt';
    } elseif ($rating > 0) {
        $label = 'Trung bình';
    } else {
        $label = 'Chưa có đánh giá';
    }
    echo "- {$rating} → '{$label}'\n";
}
echo "\n";

// 5. Test JSON decode cho hình ảnh
echo "5. Test JSON decode cho hình ảnh:\n";
$testImages = [
    '[]',
    '[{"src": "image1.jpg"}, {"src": "image2.jpg"}]',
    '["image1.jpg", "image2.jpg"]',
    '',
    null,
    'invalid json'
];

foreach ($testImages as $imgJson) {
    echo "Input: " . ($imgJson ?? 'null') . "\n";
    $decoded = json_decode($imgJson ?? '', true);
    if (is_array($decoded)) {
        $images = [];
        foreach ($decoded as $img) {
            if (is_array($img) && isset($img['src'])) {
                $images[] = $img['src'];
            } elseif (is_string($img)) {
                $images[] = $img;
            }
        }
        echo "Result: " . implode(', ', $images) . "\n";
    } else {
        echo "Result: No images\n";
    }
    echo "\n";
}

// 6. Test star calculation
echo "6. Test star calculation:\n";
$testRatings = [4.8, 4.3, 3.7, 2.1, 0];
foreach ($testRatings as $rating) {
    $fullStars = $rating ? floor($rating) : 0;
    $halfStar = ($rating && ($rating - $fullStars) >= 0.25 && ($rating - $fullStars) < 0.75) ? 1 : 0;
    $emptyStars = 5 - $fullStars - $halfStar;
    
    echo "Rating: {$rating}\n";
    echo "- Full stars: {$fullStars}\n";
    echo "- Half star: {$halfStar}\n";
    echo "- Empty stars: {$emptyStars}\n";
    echo "- Display: " . str_repeat('★', $fullStars) . ($halfStar ? '☆' : '') . str_repeat('☆', $emptyStars) . "\n\n";
}

echo "=== CÁC VẤN ĐỀ ĐÃ ĐƯỢC SỬA ===\n";
echo "✅ Lỗi readAttribute() - Thay bằng truy cập trực tiếp\n";
echo "✅ Tên trường sai - Cập nhật đúng với database\n";
echo "✅ Format số - Thêm number_format()\n";
echo "✅ Avatar initials - Xử lý tên người dùng\n";
echo "✅ Traveler type - Thêm labels tiếng Việt\n";
echo "✅ Hình ảnh đánh giá - Xử lý JSON đúng cách\n";
echo "✅ Rating stars - Tính toán sao chính xác\n";
echo "✅ Controller - Load dữ liệu đánh giá\n";
echo "✅ CSS - Thêm styling đẹp\n";
echo "✅ Error handling - Xử lý null/empty values\n";

echo "\n=== HƯỚNG DẪN SỬ DỤNG ===\n";
echo "1. Đảm bảo file CSS được load: public/css/hosting-review.css\n";
echo "2. Controller đã load: \$this->view->hosting_reviews\n";
echo "3. View sử dụng: hosting_reviews thay vì hosting.getReviews()\n";
echo "4. Tất cả method calls đã được thay bằng logic inline\n";
echo "5. Xem file BOOKING_REVIEW_FUNCTIONS.md để biết chi tiết\n";

echo "\n=== KẾT THÚC TEST ===\n";
?>
