<?php

namespace Modules\App\Models;

use Phalcon\Mvc\Model;
use Phalcon\Mvc\Model\Query;
use Phalcon\Mvc\Model\Resultset\Simple as Resultset;
use Phalcon\Mvc\Model\Manager as ModelsManager;

class HostingModel extends DatatableManagerModel
{
	public function getSource()
	{
		return "hosting";
	}

	public function columnMap()
	{
		return array(
			'id'                =>  'id',
			'host_id'           =>  'host_id',
			'title'             =>  'title',
			'code'              =>  'code',
			'type_id'           =>  'type_id',
			'image'             =>  'image',
			'imgs'              =>  'imgs',
			'content'           =>  'content',
			'qnt_room'          =>  'qnt_room',
			'qnt_guest'         =>  'qnt_guest',
			'qnt_bathroom'      =>  'qnt_bathroom',
			'qnt_bed'      			=>  'qnt_bed',
			'percentage'        =>  'percentage',
			'price'         		=>  'price',
			'price_origin'      =>  'price_origin',
			'area'          		=>  'area',
			'url_review'        =>  'url_review',
			'sticked'						=>	'sticked',
			'address'           =>  'address',
			'province_code'     =>  'province_code',
			'district_code'     =>  'district_code',
			'ward_code'         =>  'ward_code',
			'street'            =>  'street',
			'address'           =>  'address',
			'featured'          =>  'featured',
			'status'            =>  'status',
			'rental_type'       =>  'rental_type',
			'hits'              =>  'hits',
			'created'           =>  'created',
			'updated'           =>  'updated',
			'creator_id'        =>  'creator_id',
			'editor_id'         =>  'editor_id',
			'published'         =>  'published',
			'full_address'      =>  'full_address',
			'longitude'         =>  'longitude',
			'latitude'          =>  'latitude',
			'partner'          	=>  'partner',
			'seo_title'         =>  'seo_title',
			'seo_description'   =>  'seo_description',
			'seo_keywords'      =>  'seo_keywords',
			'position'          =>  'position',
			'user_id'           =>  'user_id',
			'is_booking_online' =>  'is_booking_online',
		);
	}

	public function initialize()
	{
		$this->belongsTo('host_id', '\Modules\App\Models\HostModel', 'id', ['alias' =>	'host', 'reusable' => true]);
		$this->belongsTo('type_id', '\Modules\App\Models\HostingTypeModel', 'id', ['alias' =>	'hosting_type', 'reusable' => true]);
		$this->belongsTo('district_code', '\Modules\App\Models\LocationDistrictModel', 'code', ['alias'	=>	'district', 'reusable' => true]);
		$this->belongsTo('province_code', '\Modules\App\Models\LocationProvinceModel', 'code', ['alias'	=>	'province', 'reusable' => true]);
		$this->belongsTo('ward_code', '\Modules\App\Models\LocationWardModel', 'code', ['alias'	=>	'ward', 'reusable' => true]);
		$this->belongsTo('street', '\Modules\App\Models\LocationStreetModel', 'id', ['alias' =>	'street_model', 'reusable' => true]);
		$this->belongsTo('creator_id', '\Modules\App\Models\ErpMemberModel', 'id', ['alias' => 'creator', 'reusable' => true]);
		$this->hasMany('id', '\Modules\App\Models\HostingPropertiesHasModel', 'hosting_id', ['alias' => 'hosting_properties', 'reusable' => true]);
		$this->hasOne('id', '\Modules\App\Models\HostingRulesModel', 'hosting_id', ['alias' => 'rules', 'reusable' => true]);
		$this->hasMany('id', '\Modules\App\Models\HostingRoomModel', 'hosting_id', ['alias' => 'rooms', 'reusable' => true]);
		$this->hasMany('id', '\Modules\App\Models\HostingWholeModel', 'hosting_id', ['alias' => 'wholes', 'reusable' => true]);
		$this->hasMany('id', '\Modules\App\Models\HostingVillaModel', 'hosting_id', ['alias' => 'villas', 'reusable' => true]);
		$this->hasMany('id', '\Modules\App\Models\HostingLocationDistanceModel', 'hosting_id', ['alias' => 'distances', 'reusable' => true]);
		$this->hasOne('id', '\Modules\App\Models\HostingRatingModel', 'hosting_id', ['alias' => 'rating', 'reusable' => true]);
	}

	private function getController()
	{
		return "hosting";
	}

	public function my_url($_format, $id = null)
	{
		$Ohi = new \Modules\Library\Oh\Ohi();
		return $Ohi->baseAdminUrl($this->getController() . "/index/{$_format}/{$id}");
	}

	public function datatable_column()
	{
		$province = new LocationProvinceModel();
		$type     = new HostingTypeModel();
		$init_data = array(
			array(
				'name'		=> 'h@title',
				'label' 	=> 'Tiêu đề',
				'filter'	=> array(
					'type' 	=> 'text'
				)
			),
			array(
				'name'		=> 'h@province_code',
				'label' 	=> 'Tỉnh/TP',
				'width'		=> '120px',
				'filter'	=> array(
					'type' 	=> 'select',
					'value' => $province->GetListItem()
				)
			),
			array(
				'name'		=> 'h@type_id',
				'label' 	=> 'Loại hình',
				'width'		=> '120px',
				'filter'	=> array(
					'type' 	=> 'select',
					'value' => $type->GetListItem()
				)
			),
			array(
				'name'		=> 'h@hits',
				'label' 	=> 'Lượt truy cập',
				'width'		=> '80px',
				'filter'	=> array(
					'type' 	=> 'text'
				)
			),
			array(
				'name'		=> 'h@status',
				'label' 	=> 'Hiển thị',
				'width'		=> '80px',
				'filter'	=> array(
					'type' 	=> 'select',
					'value' => array(0 => 'Không', 1 => 'Có')
				)
			),
			array(
				'name'		=> 'm@username',
				'label' 	=> 'Người tạo',
				'width'		=> '80px',
				'filter'	=> array(
					'type' 	=> 'text'
				)
			),
			array(
				'name'		=> 'h@created',
				'label' 	=> 'Ngày tạo',
				'width'		=> '100px',
				'filter'	=> array(
					'type' 	=> 'text'
				)
			),
			// array(
			// 	'name'		=> 'h@published',
			// 	'label' 	=> 'Ngày đăng',
			// 	'width'		=> '100px',
			// 	'filter'	=> array(
			// 		'type' 	=> 'text'
			// 	)
			// ),

			array(
				'name'      => 'h@id',
				'label'		=> '<a href="' . $this->my_url('add') . '" class="btn btn-primary"><i class="fa fa-plus"></i> Thêm</a>',
				'width'     => '160px',
			),
		);
		return $init_data;
	}

	public function datatable_json_data()
	{
		$result = $this->datatable_find([
			'select' 		=> ['h.title', 'h.code', 'lp.name as province_name', 'ht.code as type_code', 'ht.title as type_name', 'h.status', 'h.hits', 'h.vip', 'h.hot', 'm.username', 'h.created', 'h.published', 'h.id', 'h.id as action_id'],
			'from' 			=> ['h' => 'Modules\App\Models\HostingModel'],
			'leftJoin' 	=> [
				'Modules\App\Models\LocationProvinceModel' 	=> ['alias' => 'lp', 'on' => 'h.province_code=lp.code'],
				'Modules\App\Models\HostingTypeModel' 			=> ['alias' => 'ht', 'on' => 'h.type_id=ht.id'],
				'Modules\App\Models\ErpMemberModel' 				=> ['alias' => 'm', 'on' => 'h.creator_id=m.id']
			],
			'group_by' 	=> ['r.id'],
			'order' 		=> ['h.created' => 'desc'],
		]);

		$result->json_data = array();

		$hostingRoom = new HostingRoomModel();
		$Ohi = new \Modules\Library\Oh\Ohi();
		foreach ($result->realData as $item) {
			$action = array(
				'add-room' 		=> array('href' => $Ohi->baseAdminUrl($hostingRoom->getController() . "/index/add?hosting={$item->id}")),
				'edit' 				=> array('href' => $this->my_url('update', $item->id)),
				'delete' 			=> array('href' => $this->my_url('delete', $item->id)),
			);
			$hosting_url = ROOT_URL . '/' . $item->type_code . '/' . $item->code;
			$style = '';
			if ($item->vip == 1) {
				$style = 'color: #BF0000;';
			} elseif ($item->hot == 1) {
				$style = 'color: #F28C28;';
			}
			$hostingName = "<a href=\"$hosting_url\" target=\"_blank\" style=\"$style\"> $item->title </a>";
			$username = $item->username;
			if (empty($username)) {
				$username = "<span style=\"color: red;\">Khách</span>";
			}
			$result->json_data[] = array(
				'h@title' 					=> $hostingName,
				'h@province_code' 	=> $item->province_name,
				'h@hits' 						=> $item->hits,
				'h@type_id' 				=> $item->type_name,
				'h@status' 					=> $this->changeValue('status', $item->id, $item->status, $this->getController()),
				'm@username' 				=> $username,
				'h@created' 				=> $item->created,
				'h@published' 			=> $item->published,
				'h@id' 							=> $this->renderHtmlButton($action)
			);
		}
		return $result;
	}

	public function GetHostingItem($params = null)
	{
		$builder = $this->modelsManager->createBuilder()
			->columns("H.*")
			->from(['H' => 'Modules\App\Models\HostingModel'])
			->leftJoin('Modules\App\Models\HostingRoomModel', 'HR.hosting_id = H.id', 'HR')
			->leftJoin('Modules\App\Models\HostingPremiumModel', 'HP.hosting_id = H.id', 'HP')
			->leftJoin('Modules\App\Models\HostingTypeModel', 'H.type_id = HT.id', 'HT')
			->leftJoin('Modules\App\Models\HostingPropertiesHasModel', 'PH.hosting_id = H.id', 'PH')
			->leftJoin('Modules\App\Models\HostModel', 'H.host_id = HS.id', 'HS')
			->leftJoin('Modules\App\Models\HostingSavedModel', 'HSV.hosting_id = H.id', 'HSV')
			->leftJoin('Modules\App\Models\UserModel', 'HS.user_id = U.id', 'U')
			;

		if (!empty($params['where'])) {
			$builder->where($params['where']);
		}

		// Thêm điều kiện cho properties_id
		if (!empty($params['bind']['props'])) {
			$builder->andWhere('H.id IN (
				SELECT HPH.hosting_id FROM Modules\App\Models\HostingPropertiesHasModel AS HPH
				WHERE HPH.properties_id IN ({props:array})
				GROUP BY HPH.hosting_id
				HAVING COUNT(DISTINCT HPH.properties_id) = :countProps:
			)');
			$params['bind']['countProps'] = count($params['bind']['props']);
		}

		$order = (!empty($params['order'])) ? $params['order'] : "IF(HP.premium_type = 'vip', H.published, 0) DESC, H.published DESC";
		$builder->orderBy($order);
		if (!empty($params['limit'])) {
			$builder->limit($params['limit']);
		}

		if (!empty($params['offset'])) {
			$builder->offset($params['offset']);
		}

		$builder->groupBy("H.id");

		$bindParams = (!empty($params['bind'])) ? $params['bind'] : null;
		$result 		= $builder->getQuery()->execute($bindParams);

		return $result;
	}

	public function GetListItem()
	{
		$list = [];
		$list_item =  $this->find(array('order' => 'position ASC, title ASC'));
		foreach ($list_item as $value) {
			$list[$value->id]    = $value->title;
		}
		return $list;
	}


	public function getReviews() {
		$bookingReview = new BookingReviewModel();
		return $bookingReview->getReviewsByHosting($this->id);
	}
}
