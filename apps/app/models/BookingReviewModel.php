<?php

namespace Modules\App\Models;

use Phalcon\Mvc\Model;

class BookingReviewModel extends DatatableManagerModel
{
  public function getSource()
  {
    return "booking_review";
  }

  public function columnMap()
  {
    return [
      'id'              => 'id',
      'booking_id'      => 'booking_id',
      'overall'         => 'overall',
      'clean'           => 'clean',
      'staff'           => 'staff',
      'amenities'       => 'amenities',
      'location'        => 'location',
      'value'           => 'value',
      'title'           => 'title',
      'content'         => 'content',
      'imgs'            => 'imgs',
      'traveler_type'   => 'traveler_type',
      'is_approved'     => 'is_approved',
      'created'         => 'created'
    ];
  }

  public function initialize()
  {
    $this->belongsTo('booking_id', '\Modules\App\Models\BookingModel', 'id', ['alias' => 'booking']);
  }

  public function GetItem($params = null)
  {
    $builder = $this->modelsManager->createBuilder()
      ->columns("BR.*")
      ->from(['BR' => 'Modules\App\Models\BookingReviewModel'])
      ->leftJoin('Modules\App\Models\BookingModel', 'B.id = BR.booking_id', 'B')
      ->leftJoin('Modules\App\Models\HostingModel', 'H.id = B.hosting_id', 'H');

    if (!empty($params['where'])) {
      $builder->where($params['where']);
    }

    if (!empty($params['order'])) {
      $builder->orderBy($params['order']);
    }
    $builder->groupBy("BR.id");
    if (!empty($params['limit'])) {
      $builder->limit($params['limit']);
    }
    if (!empty($params['offset'])) {
      $builder->offset($params['offset']);
    }
    $bindParams = (!empty($params['bind'])) ? $params['bind'] : [];
    $result = $builder->getQuery()->execute($bindParams);

    return $result;
  }

  /**
   * Lấy tất cả đánh giá của một hosting
   * @param int $hostingId ID của hosting
   * @param array $options Tùy chọn bổ sung (limit, offset, order)
   * @return \Phalcon\Mvc\Model\Resultset
   */
  public function getReviewsByHosting($hostingId, $options = [])
  {
    $params = [
      'where' => 'B.hosting_id = :hosting_id: AND BR.is_approved = 1',
      'bind'  => ['hosting_id' => $hostingId],
      'order' => $options['order'] ?? 'BR.created DESC'
    ];

    if (!empty($options['limit'])) {
      $params['limit'] = $options['limit'];
    }

    if (!empty($options['offset'])) {
      $params['offset'] = $options['offset'];
    }

    return $this->GetItem($params);
  }

  /**
   * Tính toán điểm trung bình của hosting theo từng tiêu chí
   * @param int $hostingId ID của hosting
   * @return array|false Mảng chứa điểm trung bình hoặc false nếu không có đánh giá
   */
  public function calculateHostingRating($hostingId)
  {
    $reviews = $this->getReviewsByHosting($hostingId);

    if (!$reviews || count($reviews) == 0) {
      return false;
    }

    $totals = [
      'overall'   => 0,
      'clean'     => 0,
      'staff'     => 0,
      'amenities' => 0,
      'location'  => 0,
      'value'     => 0
    ];

    $count = 0;
    foreach ($reviews as $review) {
      $totals['overall']   += (float)$review->overall;
      $totals['clean']     += (float)$review->clean;
      $totals['staff']     += (float)$review->staff;
      $totals['amenities'] += (float)$review->amenities;
      $totals['location']  += (float)$review->location;
      $totals['value']     += (float)$review->value;
      $count++;
    }

    return [
      'total_reviews' => $count,
      'overall_avg'   => round($totals['overall'] / $count, 1),
      'clean_avg'     => round($totals['clean'] / $count, 1),
      'staff_avg'     => round($totals['staff'] / $count, 1),
      'amenities_avg' => round($totals['amenities'] / $count, 1),
      'location_avg'  => round($totals['location'] / $count, 1),
      'value_avg'     => round($totals['value'] / $count, 1)
    ];
  }

  /**
   * Lấy điểm trung bình tổng thể của hosting
   * @param int $hostingId ID của hosting
   * @return float|null Điểm trung bình hoặc null nếu không có đánh giá
   */
  public function getHostingOverallRating($hostingId)
  {
    $rating = $this->calculateHostingRating($hostingId);
    return $rating ? $rating['overall_avg'] : null;
  }

  /**
   * Lấy thống kê đánh giá theo từng mức điểm (1-5 sao)
   * @param int $hostingId ID của hosting
   * @return array Mảng thống kê số lượng đánh giá theo từng mức điểm
   */
  public function getHostingRatingDistribution($hostingId)
  {
    $reviews = $this->getReviewsByHosting($hostingId);

    $distribution = [
      5 => 0,
      4 => 0,
      3 => 0,
      2 => 0,
      1 => 0
    ];

    foreach ($reviews as $review) {
      $overall = (int)round($review->overall);
      if ($overall >= 1 && $overall <= 5) {
        $distribution[$overall]++;
      }
    }

    return $distribution;
  }

  /**
   * Lấy top đánh giá tích cực nhất của hosting
   * @param int $hostingId ID của hosting
   * @param int $limit Số lượng đánh giá cần lấy
   * @return \Phalcon\Mvc\Model\Resultset
   */
  public function getTopPositiveReviews($hostingId, $limit = 5)
  {
    return $this->getReviewsByHosting($hostingId, [
      'order' => 'BR.overall DESC, BR.created DESC',
      'limit' => $limit
    ]);
  }

  /**
   * Lấy đánh giá mới nhất của hosting
   * @param int $hostingId ID của hosting
   * @param int $limit Số lượng đánh giá cần lấy
   * @return \Phalcon\Mvc\Model\Resultset
   */
  public function getLatestReviews($hostingId, $limit = 10)
  {
    return $this->getReviewsByHosting($hostingId, [
      'order' => 'BR.created DESC',
      'limit' => $limit
    ]);
  }

  /**
   * Lấy đánh giá theo loại khách du lịch
   * @param int $hostingId ID của hosting
   * @param string $travelerType Loại khách du lịch
   * @param int $limit Số lượng đánh giá cần lấy
   * @return \Phalcon\Mvc\Model\Resultset
   */
  public function getReviewsByTravelerType($hostingId, $travelerType, $limit = 10)
  {
    $params = [
      'where' => 'B.hosting_id = :hosting_id: AND BR.is_approved = 1 AND BR.traveler_type = :traveler_type:',
      'bind'  => [
        'hosting_id'    => $hostingId,
        'traveler_type' => $travelerType
      ],
      'order' => 'BR.created DESC',
      'limit' => $limit
    ];

    return $this->GetItem($params);
  }

  /**
   * Lấy thống kê đánh giá theo tháng
   * @param int $hostingId ID của hosting
   * @param int $months Số tháng gần đây (mặc định 12 tháng)
   * @return array Mảng thống kê theo tháng
   */
  public function getMonthlyRatingStats($hostingId, $months = 12)
  {
    $builder = $this->modelsManager->createBuilder()
      ->columns([
        'YEAR(BR.created) as year',
        'MONTH(BR.created) as month',
        'COUNT(*) as total_reviews',
        'AVG(BR.overall) as avg_rating'
      ])
      ->from(['BR' => 'Modules\App\Models\BookingReviewModel'])
      ->leftJoin('Modules\App\Models\BookingModel', 'B.id = BR.booking_id', 'B')
      ->where('B.hosting_id = :hosting_id: AND BR.is_approved = 1 AND BR.created >= DATE_SUB(NOW(), INTERVAL :months: MONTH)')
      ->groupBy('YEAR(BR.created), MONTH(BR.created)')
      ->orderBy('year DESC, month DESC');

    $result = $builder->getQuery()->execute([
      'hosting_id' => $hostingId,
      'months'     => $months
    ]);

    $stats = [];
    foreach ($result as $row) {
      $stats[] = [
        'year'          => $row->year,
        'month'         => $row->month,
        'total_reviews' => $row->total_reviews,
        'avg_rating'    => round($row->avg_rating, 1)
      ];
    }

    return $stats;
  }

  /**
   * So sánh điểm đánh giá với trung bình của cùng loại hosting
   * @param int $hostingId ID của hosting
   * @return array|false Kết quả so sánh hoặc false nếu không có dữ liệu
   */
  public function compareWithSimilarHostings($hostingId)
  {
    // Lấy thông tin hosting hiện tại
    $hosting = \Modules\App\Models\HostingModel::findFirst($hostingId);
    if (!$hosting) {
      return false;
    }

    // Tính điểm trung bình của hosting hiện tại
    $currentRating = $this->calculateHostingRating($hostingId);
    if (!$currentRating) {
      return false;
    }

    // Tính điểm trung bình của các hosting cùng loại trong cùng tỉnh
    $builder = $this->modelsManager->createBuilder()
      ->columns([
        'AVG(BR.overall) as avg_overall',
        'AVG(BR.clean) as avg_clean',
        'AVG(BR.staff) as avg_staff',
        'AVG(BR.amenities) as avg_amenities',
        'AVG(BR.location) as avg_location',
        'AVG(BR.value) as avg_value'
      ])
      ->from(['BR' => 'Modules\App\Models\BookingReviewModel'])
      ->leftJoin('Modules\App\Models\BookingModel', 'B.id = BR.booking_id', 'B')
      ->leftJoin('Modules\App\Models\HostingModel', 'H.id = B.hosting_id', 'H')
      ->where('H.type_id = :type_id: AND H.province_code = :province_code: AND H.id != :hosting_id: AND BR.is_approved = 1');

    $result = $builder->getQuery()->execute([
      'type_id'       => $hosting->type_id,
      'province_code' => $hosting->province_code,
      'hosting_id'    => $hostingId
    ]);

    if (!$result || count($result) == 0) {
      return false;
    }

    $similarAvg = $result->getFirst();

    return [
      'current' => $currentRating,
      'similar_avg' => [
        'overall_avg'   => round($similarAvg->avg_overall, 1),
        'clean_avg'     => round($similarAvg->avg_clean, 1),
        'staff_avg'     => round($similarAvg->avg_staff, 1),
        'amenities_avg' => round($similarAvg->avg_amenities, 1),
        'location_avg'  => round($similarAvg->avg_location, 1),
        'value_avg'     => round($similarAvg->avg_value, 1)
      ],
      'comparison' => [
        'overall'   => $currentRating['overall_avg'] - round($similarAvg->avg_overall, 1),
        'clean'     => $currentRating['clean_avg'] - round($similarAvg->avg_clean, 1),
        'staff'     => $currentRating['staff_avg'] - round($similarAvg->avg_staff, 1),
        'amenities' => $currentRating['amenities_avg'] - round($similarAvg->avg_amenities, 1),
        'location'  => $currentRating['location_avg'] - round($similarAvg->avg_location, 1),
        'value'     => $currentRating['value_avg'] - round($similarAvg->avg_value, 1)
      ]
    ];
  }

  /**
   * Lấy từ khóa được nhắc đến nhiều nhất trong đánh giá
   * @param int $hostingId ID của hosting
   * @param int $limit Số lượng từ khóa cần lấy
   * @return array Mảng từ khóa và số lần xuất hiện
   */
  public function getMostMentionedKeywords($hostingId, $limit = 10)
  {
    $reviews = $this->getReviewsByHosting($hostingId);

    $allText = '';
    foreach ($reviews as $review) {
      $allText .= ' ' . $review->title . ' ' . $review->content;
    }

    // Loại bỏ các từ phổ biến và ký tự đặc biệt
    $commonWords = ['và', 'của', 'có', 'là', 'được', 'rất', 'tôi', 'chúng', 'này', 'đó', 'với', 'cho', 'từ', 'trong', 'về'];
    $words = str_word_count(strtolower($allText), 1, 'àáạảãâầấậẩẫăằắặẳẵèéẹẻẽêềếệểễìíịỉĩòóọỏõôồốộổỗơờớợởỡùúụủũưừứựửữỳýỵỷỹđ');

    $wordCount = [];
    foreach ($words as $word) {
      if (strlen($word) > 2 && !in_array($word, $commonWords)) {
        $wordCount[$word] = isset($wordCount[$word]) ? $wordCount[$word] + 1 : 1;
      }
    }

    arsort($wordCount);
    return array_slice($wordCount, 0, $limit, true);
  }

  /**
   * Cập nhật bảng hosting_rating sau khi có đánh giá mới
   * @param int $hostingId ID của hosting
   * @return bool Kết quả cập nhật
   */
  public function updateHostingRatingTable($hostingId)
  {
    $rating = $this->calculateHostingRating($hostingId);
    if (!$rating) {
      return false;
    }

    $hostingRating = \Modules\App\Models\HostingRatingModel::findFirst([
      'conditions' => 'hosting_id = :hosting_id:',
      'bind'       => ['hosting_id' => $hostingId]
    ]);

    if (!$hostingRating) {
      $hostingRating = new \Modules\App\Models\HostingRatingModel();
      $hostingRating->hosting_id = $hostingId;
    }

    $hostingRating->assign($rating);
    $hostingRating->updated = date('Y-m-d H:i:s');

    return $hostingRating->save();
  }

  /**
   * Lấy tên hiển thị cho avatar (tên + họ cuối)
   * Nếu không có user_fullname, trả về 'Ẩn danh'
   * @return string
   */
  public function getAvatarWithName()
  {
    if (!$this->booking || empty($this->booking->user_fullname)) {
      return 'Ẩn danh';
    }
    $fullname = trim($this->booking->user_fullname);
    $parts = preg_split('/\s+/', $fullname);
    $first = mb_substr($parts[0], 0, 1, 'UTF-8');
    $last = mb_substr($parts[count($parts) - 1], 0, 1, 'UTF-8');
    return $first . $last;
  }

  /**
   * Lấy tên hiển thị của người dùng
   * @return string
   */
  public function getUserDisplayName()
  {
    if ($this->booking && $this->booking->user_fullname) {
      return $this->booking->user_fullname;
    }
    return 'Khách hàng';
  }

  /**
   * Lấy danh sách hình ảnh đánh giá đã được parse
   * @return array
   */
  public function getParsedImages()
  {
    if (empty($this->imgs)) {
      return [];
    }

    $images = json_decode($this->imgs, true);
    if (!is_array($images)) {
      return [];
    }

    $result = [];
    foreach ($images as $img) {
      if (is_array($img) && isset($img['src'])) {
        $result[] = $img['src'];
      } elseif (is_string($img)) {
        $result[] = $img;
      }
    }

    return $result;
  }

  /**
   * Format ngày tạo đánh giá
   * @param string $format Format ngày (mặc định d/m/Y)
   * @return string
   */
  public function getFormattedDate($format = 'd/m/Y')
  {
    return date($format, strtotime($this->created));
  }

  /**
   * Lấy label cho loại khách du lịch
   * @return string
   */
  public function getTravelerTypeLabel()
  {
    $types = [
      'business'    => 'Công tác',
      'leisure'     => 'Du lịch',
      'family'      => 'Gia đình',
      'couple'      => 'Cặp đôi',
      'friends'     => 'Bạn bè',
      'solo'        => 'Một mình'
    ];

    return isset($types[$this->traveler_type]) ? $types[$this->traveler_type] : $this->traveler_type;
  }
}
