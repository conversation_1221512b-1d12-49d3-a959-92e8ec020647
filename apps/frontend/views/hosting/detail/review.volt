<!-- <PERSON><PERSON><PERSON> giá từ khách hàng - HTML sử dụng BEM methodology -->
<div class="customer-reviews">
  <!-- Header -->
  <div class="customer-reviews__header">
    <h1 class="customer-reviews__title"><PERSON><PERSON><PERSON> giá từ khách hàng</h1>
    <p class="customer-reviews__subtitle">Chia sẻ từ những khách hàng đã trải nghiệm</p>
  </div>

  <!-- Rating Summary Section -->
  <div class="customer-reviews__summary">
    <!-- Overall Rating -->
    <div class="rating-summary">
      <div class="rating-summary__card">
        <div class="rating-summary__content">
          <div class="rating-summary__badge">
            <div class="rating-summary__score">8.8</div>
          </div>
          <h2 class="rating-summary__label">Rất tốt</h2>
          <p class="rating-summary__count">Dựa trên 247 đánh giá</p>
          <a href="#" class="rating-summary__link"><PERSON>em thêm đánh giá</a>
        </div>
      </div>
    </div>
    <!-- Rating Categories -->
    <div class="rating-breakdown">
      <div class="rating-breakdown__card">
        <div class="rating-breakdown__content">
          <h3 class="rating-breakdown__title">Chi tiết đánh giá</h3>
          <div class="rating-breakdown__list">
            <div class="rating-metric">
              <span class="rating-metric__label">Tiện nghi</span>
              <div class="rating-metric__bar">
                <div class="rating-metric__progress">
                  <div class="rating-metric__fill" style="width:90%"></div>
                </div>
              </div>
              <span class="rating-metric__score">9.0</span>
            </div>
            <div class="rating-metric">
              <span class="rating-metric__label">Thoải mái</span>
              <div class="rating-metric__bar">
                <div class="rating-metric__progress">
                  <div class="rating-metric__fill" style="width:90%"></div>
                </div>
              </div>
              <span class="rating-metric__score">9.0</span>
            </div>
            <div class="rating-metric">
              <span class="rating-metric__label">Sạch sẽ</span>
              <div class="rating-metric__bar">
                <div class="rating-metric__progress">
                  <div class="rating-metric__fill" style="width:90%"></div>
                </div>
              </div>
              <span class="rating-metric__score">9.0</span>
            </div>
            <div class="rating-metric">
              <span class="rating-metric__label">Nhân viên phục vụ</span>
              <div class="rating-metric__bar">
                <div class="rating-metric__progress">
                  <div class="rating-metric__fill" style="width:70%"></div>
                </div>
              </div>
              <span class="rating-metric__score">7.0</span>
            </div>
            <div class="rating-metric">
              <span class="rating-metric__label">Đáng giá tiền</span>
              <div class="rating-metric__bar">
                <div class="rating-metric__progress">
                  <div class="rating-metric__fill" style="width:70%"></div>
                </div>
              </div>
              <span class="rating-metric__score">7.0</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Filters and Controls -->
  <div class="reviews-controls">
    <div class="reviews-controls__info">
      <h3 class="reviews-controls__title">Phản hồi từ khách lưu trú</h3>
      <p class="reviews-controls__count">5 đánh giá</p>
    </div>
    <div class="reviews-controls__actions">
      <button class="reviews-controls__sort-btn">
        <!-- Filter icon -->
        <svg class="reviews-controls__icon" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24"><path d="M4 6h16M6 12h12M10 18h4"/></svg>
        Sắp xếp
        <svg class="reviews-controls__icon" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24"><path d="M6 9l6 6 6-6"/></svg>
      </button>
    </div>
  </div>

  <!-- Star Filter -->
  <div class="star-filter">
    <button class="star-filter__btn star-filter__btn--active">Tất cả</button>
    <button class="star-filter__btn">5 Sao</button>
    <button class="star-filter__btn">4 Sao</button>
    <button class="star-filter__btn">3 Sao</button>
    <button class="star-filter__btn">2 Sao</button>
    <button class="star-filter__btn">1 Sao</button>
  </div>

  <!-- Reviews List -->
  <div class="reviews-list">
    <!-- Review 1 -->
    <div class="review-item">
      <div class="review-item__content">
        <div class="review-item__layout">
          <div class="review-item__avatar">
            <img src="/placeholder-user.jpg" alt="Minh Anh" class="review-item__avatar-img" />
          </div>
          <div class="review-item__main">
            <div class="review-item__header">
              <div class="review-item__user-info">
                <h4 class="review-item__name">Minh Anh</h4>
                <p class="review-item__location">Hà Nội, Việt Nam • Hôm nay</p>
              </div>
              <div class="review-item__rating">
                <!-- 5 stars -->
                <svg class="review-item__star review-item__star--filled" viewBox="0 0 24 24"><polygon points="12,2 15,9 22,9 17,14 18,21 12,17 6,21 7,14 2,9 9,9"/></svg>
                <svg class="review-item__star review-item__star--filled" viewBox="0 0 24 24"><polygon points="12,2 15,9 22,9 17,14 18,21 12,17 6,21 7,14 2,9 9,9"/></svg>
                <svg class="review-item__star review-item__star--filled" viewBox="0 0 24 24"><polygon points="12,2 15,9 22,9 17,14 18,21 12,17 6,21 7,14 2,9 9,9"/></svg>
                <svg class="review-item__star review-item__star--filled" viewBox="0 0 24 24"><polygon points="12,2 15,9 22,9 17,14 18,21 12,17 6,21 7,14 2,9 9,9"/></svg>
                <svg class="review-item__star review-item__star--filled" viewBox="0 0 24 24"><polygon points="12,2 15,9 22,9 17,14 18,21 12,17 6,21 7,14 2,9 9,9"/></svg>
              </div>
            </div>
            <p class="review-item__text">Khách sạn tuyệt vời với vị trí thuận lợi ngay trung tâm thành phố. Phòng ốc sạch sẽ, thoáng mát và được trang bị đầy đủ tiện nghi. Nhân viên phục vụ rất thân thiện và nhiệt tình. Bữa sáng buffet đa dạng và ngon miệng. Chúng tôi sẽ quay lại lần sau!</p>
            <!-- Image Gallery -->
            <div class="review-item__gallery">
              <div class="review-gallery">
                <img src="/placeholder.svg?height=200&width=300" alt="Review image 1" class="review-gallery__image" />
                <img src="/placeholder.svg?height=200&width=300" alt="Review image 2" class="review-gallery__image" />
                <img src="/placeholder.svg?height=200&width=300" alt="Review image 3" class="review-gallery__image" />
                <img src="/placeholder.svg?height=200&width=300" alt="Review image 4" class="review-gallery__image" />
              </div>
            </div>
            <div class="review-item__actions">
              <button class="review-action">
                <!-- ThumbsUp icon -->
                <svg class="review-action__icon" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24"><path d="M14 9V5a3 3 0 0 0-6 0v4"/><path d="M5 15V9a2 2 0 0 1 2-2h9.28a2 2 0 0 1 1.94 2.45l-1.38 6.9A2 2 0 0 1 15.9 19H7a2 2 0 0 1-2-2z"/></svg>
                <span class="review-action__text">Hữu ích (12)</span>
              </button>
              <button class="review-action">
                <!-- MessageCircle icon -->
                <svg class="review-action__icon" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24"><path d="M21 11.5a8.38 8.38 0 0 1-1.9 5.4A8.5 8.5 0 0 1 3 12.5c0-4.7 3.8-8.5 8.5-8.5s8.5 3.8 8.5 8.5z"/><path d="M8.5 14.5h.01"/><path d="M12 14.5h.01"/><path d="M15.5 14.5h.01"/></svg>
                <span class="review-action__text">Trả lời</span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- Review 2 -->
    <div class="bg-white rounded-xl shadow hover:shadow-md transition-shadow">
      <div class="p-8">
        <div class="flex gap-6">
          <div class="w-14 h-14 flex-shrink-0 rounded-full overflow-hidden bg-gray-200 flex items-center justify-center">
            <img src="/placeholder-user.jpg" alt="Thanh Hương" class="w-full h-full object-cover" />
          </div>
          <div class="flex-1 space-y-4">
            <div class="flex items-start justify-between">
              <div>
                <h4 class="text-lg font-semibold">Thanh Hương</h4>
                <p class="text-gray-500">TP. Hồ Chí Minh, Việt Nam • 2 ngày trước</p>
              </div>
              <div class="flex items-center gap-1">
                <!-- 4 stars -->
                <svg class="w-5 h-5 fill-yellow-400 text-yellow-400" viewBox="0 0 24 24"><polygon points="12,2 15,9 22,9 17,14 18,21 12,17 6,21 7,14 2,9 9,9"/></svg>
                <svg class="w-5 h-5 fill-yellow-400 text-yellow-400" viewBox="0 0 24 24"><polygon points="12,2 15,9 22,9 17,14 18,21 12,17 6,21 7,14 2,9 9,9"/></svg>
                <svg class="w-5 h-5 fill-yellow-400 text-yellow-400" viewBox="0 0 24 24"><polygon points="12,2 15,9 22,9 17,14 18,21 12,17 6,21 7,14 2,9 9,9"/></svg>
                <svg class="w-5 h-5 fill-yellow-400 text-yellow-400" viewBox="0 0 24 24"><polygon points="12,2 15,9 22,9 17,14 18,21 12,17 6,21 7,14 2,9 9,9"/></svg>
                <svg class="w-5 h-5 text-gray-300" viewBox="0 0 24 24"><polygon points="12,2 15,9 22,9 17,14 18,21 12,17 6,21 7,14 2,9 9,9"/></svg>
              </div>
            </div>
            <p class="text-gray-700 leading-relaxed text-base">Khách sạn có vị trí tốt, gần nhiều điểm tham quan. Phòng khá sạch sẽ nhưng hơi nhỏ so với mong đợi. Dịch vụ lễ tân tốt, check-in nhanh chóng. Điểm trừ là wifi hơi chậm và tiếng ồn từ đường phố vào ban đêm. Nhìn chung vẫn là một lựa chọn ổn với mức giá này.</p>
            <!-- Image Gallery -->
            <div class="mt-4">
              <div class="grid grid-cols-4 gap-3">
                <img src="/placeholder.svg?height=200&width=300" alt="Review image 1" class="w-full h-24 object-cover rounded-lg" />
                <img src="/placeholder.svg?height=200&width=300" alt="Review image 2" class="w-full h-24 object-cover rounded-lg" />
              </div>
            </div>
            <div class="flex items-center gap-6 pt-4 border-t">
              <button class="flex items-center gap-2 text-gray-600 hover:text-gray-800 bg-transparent border-0 p-0">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24"><path d="M14 9V5a3 3 0 0 0-6 0v4"/><path d="M5 15V9a2 2 0 0 1 2-2h9.28a2 2 0 0 1 1.94 2.45l-1.38 6.9A2 2 0 0 1 15.9 19H7a2 2 0 0 1-2-2z"/></svg>
                Hữu ích (8)
              </button>
              <button class="flex items-center gap-2 text-gray-600 hover:text-gray-800 bg-transparent border-0 p-0">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24"><path d="M21 11.5a8.38 8.38 0 0 1-1.9 5.4A8.5 8.5 0 0 1 3 12.5c0-4.7 3.8-8.5 8.5-8.5s8.5 3.8 8.5 8.5z"/><path d="M8.5 14.5h.01"/><path d="M12 14.5h.01"/><path d="M15.5 14.5h.01"/></svg>
                Trả lời
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- Review 3 -->
    <div class="bg-white rounded-xl shadow hover:shadow-md transition-shadow">
      <div class="p-8">
        <div class="flex gap-6">
          <div class="w-14 h-14 flex-shrink-0 rounded-full overflow-hidden bg-gray-200 flex items-center justify-center">
            <img src="/placeholder-user.jpg" alt="Đức Minh" class="w-full h-full object-cover" />
          </div>
          <div class="flex-1 space-y-4">
            <div class="flex items-start justify-between">
              <div>
                <h4 class="text-lg font-semibold">Đức Minh</h4>
                <p class="text-gray-500">Đà Nẵng, Việt Nam • 1 tuần trước</p>
              </div>
              <div class="flex items-center gap-1">
                <!-- 5 stars -->
                <svg class="w-5 h-5 fill-yellow-400 text-yellow-400" viewBox="0 0 24 24"><polygon points="12,2 15,9 22,9 17,14 18,21 12,17 6,21 7,14 2,9 9,9"/></svg>
                <svg class="w-5 h-5 fill-yellow-400 text-yellow-400" viewBox="0 0 24 24"><polygon points="12,2 15,9 22,9 17,14 18,21 12,17 6,21 7,14 2,9 9,9"/></svg>
                <svg class="w-5 h-5 fill-yellow-400 text-yellow-400" viewBox="0 0 24 24"><polygon points="12,2 15,9 22,9 17,14 18,21 12,17 6,21 7,14 2,9 9,9"/></svg>
                <svg class="w-5 h-5 fill-yellow-400 text-yellow-400" viewBox="0 0 24 24"><polygon points="12,2 15,9 22,9 17,14 18,21 12,17 6,21 7,14 2,9 9,9"/></svg>
                <svg class="w-5 h-5 fill-yellow-400 text-yellow-400" viewBox="0 0 24 24"><polygon points="12,2 15,9 22,9 17,14 18,21 12,17 6,21 7,14 2,9 9,9"/></svg>
              </div>
            </div>
            <p class="text-gray-700 leading-relaxed text-base">Rất hài lòng với chuyến lưu trú này! Phòng rộng rãi, view đẹp nhìn ra thành phố. Giường ngủ thoải mái, phòng tắm hiện đại. Đặc biệt ấn tượng với dịch vụ room service và nhà hàng trong khách sạn. Nhân viên chuyên nghiệp và luôn sẵn sàng hỗ trợ. Chắc chắn sẽ giới thiệu cho bạn bè!</p>
            <!-- Image Gallery -->
            <div class="mt-4">
              <div class="grid grid-cols-4 gap-3">
                <img src="/placeholder.svg?height=200&width=300" alt="Review image 1" class="w-full h-24 object-cover rounded-lg" />
                <img src="/placeholder.svg?height=200&width=300" alt="Review image 2" class="w-full h-24 object-cover rounded-lg" />
                <img src="/placeholder.svg?height=200&width=300" alt="Review image 3" class="w-full h-24 object-cover rounded-lg" />
                <img src="/placeholder.svg?height=200&width=300" alt="Review image 4" class="w-full h-24 object-cover rounded-lg" />
              </div>
            </div>
            <div class="flex items-center gap-6 pt-4 border-t">
              <button class="flex items-center gap-2 text-gray-600 hover:text-gray-800 bg-transparent border-0 p-0">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24"><path d="M14 9V5a3 3 0 0 0-6 0v4"/><path d="M5 15V9a2 2 0 0 1 2-2h9.28a2 2 0 0 1 1.94 2.45l-1.38 6.9A2 2 0 0 1 15.9 19H7a2 2 0 0 1-2-2z"/></svg>
                Hữu ích (15)
              </button>
              <button class="flex items-center gap-2 text-gray-600 hover:text-gray-800 bg-transparent border-0 p-0">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24"><path d="M21 11.5a8.38 8.38 0 0 1-1.9 5.4A8.5 8.5 0 0 1 3 12.5c0-4.7 3.8-8.5 8.5-8.5s8.5 3.8 8.5 8.5z"/><path d="M8.5 14.5h.01"/><path d="M12 14.5h.01"/><path d="M15.5 14.5h.01"/></svg>
                Trả lời
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- Review 4 -->
    <div class="bg-white rounded-xl shadow hover:shadow-md transition-shadow">
      <div class="p-8">
        <div class="flex gap-6">
          <div class="w-14 h-14 flex-shrink-0 rounded-full overflow-hidden bg-gray-200 flex items-center justify-center">
            <img src="/placeholder-user.jpg" alt="Lan Phương" class="w-full h-full object-cover" />
          </div>
          <div class="flex-1 space-y-4">
            <div class="flex items-start justify-between">
              <div>
                <h4 class="text-lg font-semibold">Lan Phương</h4>
                <p class="text-gray-500">Cần Thơ, Việt Nam • 2 tuần trước</p>
              </div>
              <div class="flex items-center gap-1">
                <!-- 4 stars -->
                <svg class="w-5 h-5 fill-yellow-400 text-yellow-400" viewBox="0 0 24 24"><polygon points="12,2 15,9 22,9 17,14 18,21 12,17 6,21 7,14 2,9 9,9"/></svg>
                <svg class="w-5 h-5 fill-yellow-400 text-yellow-400" viewBox="0 0 24 24"><polygon points="12,2 15,9 22,9 17,14 18,21 12,17 6,21 7,14 2,9 9,9"/></svg>
                <svg class="w-5 h-5 fill-yellow-400 text-yellow-400" viewBox="0 0 24 24"><polygon points="12,2 15,9 22,9 17,14 18,21 12,17 6,21 7,14 2,9 9,9"/></svg>
                <svg class="w-5 h-5 fill-yellow-400 text-yellow-400" viewBox="0 0 24 24"><polygon points="12,2 15,9 22,9 17,14 18,21 12,17 6,21 7,14 2,9 9,9"/></svg>
                <svg class="w-5 h-5 text-gray-300" viewBox="0 0 24 24"><polygon points="12,2 15,9 22,9 17,14 18,21 12,17 6,21 7,14 2,9 9,9"/></svg>
              </div>
            </div>
            <p class="text-gray-700 leading-relaxed text-base">Khách sạn có thiết kế đẹp và hiện đại. Vị trí thuận tiện để đi lại. Tuy nhiên, dịch vụ dọn phòng chưa được chu đáo lắm, có lúc phải nhắc nhở mới được thay khăn tắm. Bữa sáng cần đa dạng hơn. Giá cả hợp lý, phù hợp cho nhóm bạn trẻ.</p>
            <!-- Không có ảnh -->
            <div class="flex items-center gap-6 pt-4 border-t">
              <button class="flex items-center gap-2 text-gray-600 hover:text-gray-800 bg-transparent border-0 p-0">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24"><path d="M14 9V5a3 3 0 0 0-6 0v4"/><path d="M5 15V9a2 2 0 0 1 2-2h9.28a2 2 0 0 1 1.94 2.45l-1.38 6.9A2 2 0 0 1 15.9 19H7a2 2 0 0 1-2-2z"/></svg>
                Hữu ích (6)
              </button>
              <button class="flex items-center gap-2 text-gray-600 hover:text-gray-800 bg-transparent border-0 p-0">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24"><path d="M21 11.5a8.38 8.38 0 0 1-1.9 5.4A8.5 8.5 0 0 1 3 12.5c0-4.7 3.8-8.5 8.5-8.5s8.5 3.8 8.5 8.5z"/><path d="M8.5 14.5h.01"/><path d="M12 14.5h.01"/><path d="M15.5 14.5h.01"/></svg>
                Trả lời
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- Review 5 -->
    <div class="bg-white rounded-xl shadow hover:shadow-md transition-shadow">
      <div class="p-8">
        <div class="flex gap-6">
          <div class="w-14 h-14 flex-shrink-0 rounded-full overflow-hidden bg-gray-200 flex items-center justify-center">
            <img src="/placeholder-user.jpg" alt="Hoàng Nam" class="w-full h-full object-cover" />
          </div>
          <div class="flex-1 space-y-4">
            <div class="flex items-start justify-between">
              <div>
                <h4 class="text-lg font-semibold">Hoàng Nam</h4>
                <p class="text-gray-500">Hải Phòng, Việt Nam • 3 tuần trước</p>
              </div>
              <div class="flex items-center gap-1">
                <!-- 5 stars -->
                <svg class="w-5 h-5 fill-yellow-400 text-yellow-400" viewBox="0 0 24 24"><polygon points="12,2 15,9 22,9 17,14 18,21 12,17 6,21 7,14 2,9 9,9"/></svg>
                <svg class="w-5 h-5 fill-yellow-400 text-yellow-400" viewBox="0 0 24 24"><polygon points="12,2 15,9 22,9 17,14 18,21 12,17 6,21 7,14 2,9 9,9"/></svg>
                <svg class="w-5 h-5 fill-yellow-400 text-yellow-400" viewBox="0 0 24 24"><polygon points="12,2 15,9 22,9 17,14 18,21 12,17 6,21 7,14 2,9 9,9"/></svg>
                <svg class="w-5 h-5 fill-yellow-400 text-yellow-400" viewBox="0 0 24 24"><polygon points="12,2 15,9 22,9 17,14 18,21 12,17 6,21 7,14 2,9 9,9"/></svg>
                <svg class="w-5 h-5 fill-yellow-400 text-yellow-400" viewBox="0 0 24 24"><polygon points="12,2 15,9 22,9 17,14 18,21 12,17 6,21 7,14 2,9 9,9"/></svg>
              </div>
            </div>
            <p class="text-gray-700 leading-relaxed text-base">Trải nghiệm tuyệt vời! Khách sạn có thiết kế sang trọng, phòng ốc rộng rãi và thoáng đãng. Đặc biệt ấn tượng với hồ bơi trên tầng thượng và spa. Nhân viên rất chu đáo và chuyên nghiệp. Breakfast buffet rất phong phú với nhiều món Á và Âu.</p>
            <!-- Image Gallery -->
            <div class="mt-4">
              <div class="grid grid-cols-4 gap-3">
                <img src="/placeholder.svg?height=200&width=300" alt="Review image 1" class="w-full h-24 object-cover rounded-lg" />
                <img src="/placeholder.svg?height=200&width=300" alt="Review image 2" class="w-full h-24 object-cover rounded-lg" />
                <img src="/placeholder.svg?height=200&width=300" alt="Review image 3" class="w-full h-24 object-cover rounded-lg" />
              </div>
            </div>
            <div class="flex items-center gap-6 pt-4 border-t">
              <button class="flex items-center gap-2 text-gray-600 hover:text-gray-800 bg-transparent border-0 p-0">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24"><path d="M14 9V5a3 3 0 0 0-6 0v4"/><path d="M5 15V9a2 2 0 0 1 2-2h9.28a2 2 0 0 1 1.94 2.45l-1.38 6.9A2 2 0 0 1 15.9 19H7a2 2 0 0 1-2-2z"/></svg>
                Hữu ích (9)
              </button>
              <button class="flex items-center gap-2 text-gray-600 hover:text-gray-800 bg-transparent border-0 p-0">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24"><path d="M21 11.5a8.38 8.38 0 0 1-1.9 5.4A8.5 8.5 0 0 1 3 12.5c0-4.7 3.8-8.5 8.5-8.5s8.5 3.8 8.5 8.5z"/><path d="M8.5 14.5h.01"/><path d="M12 14.5h.01"/><path d="M15.5 14.5h.01"/></svg>
                Trả lời
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Load More -->
  <div class="text-center pt-8">
    <button class="border rounded px-12 py-3 text-lg bg-white">Xem thêm đánh giá</button>
  </div>
</div>
<style>
  /* CSS thuần cho giao diện review khách hàng */

.max-w-6xl {
  max-width: 72rem;
}
.mx-auto {
  margin-left: auto;
  margin-right: auto;
}
.p-6 {
  padding: 1.5rem;
}
.p-8 {
  padding: 2rem;
}
.pt-4 {
  padding-top: 1rem;
}
.pt-8 {
  padding-top: 2rem;
}
.mb-2 {
  margin-bottom: 0.5rem;
}
.mb-4 {
  margin-bottom: 1rem;
}
.mb-6 {
  margin-bottom: 1.5rem;
}
.space-y-2 > * + * {
  margin-top: 0.5rem;
}
.space-y-4 > * + * {
  margin-top: 1rem;
}
.space-y-6 > * + * {
  margin-top: 1.5rem;
}
.text-center {
  text-align: center;
}
.text-3xl {
  font-size: 1.875rem;
  font-weight: bold;
}
.text-2xl {
  font-size: 1.5rem;
  font-weight: bold;
}
.text-xl {
  font-size: 1.25rem;
  font-weight: bold;
}
.text-lg {
  font-size: 1.125rem;
  font-weight: 600;
}
.text-base {
  font-size: 1rem;
}
.text-sm {
  font-size: 0.875rem;
}
.text-gray-600 {
  color: #4b5563;
}
.text-gray-500 {
  color: #6b7280;
}
.text-gray-700 {
  color: #374151;
}
.text-gray-800 {
  color: #1f2937;
}
.text-white {
  color: #fff;
}
.text-yellow-400 {
  color: #facc15;
}
.font-bold {
  font-weight: bold;
}
.font-semibold {
  font-weight: 600;
}
.bg-white {
  background: #fff;
}
.bg-green-500 {
  background: #22c55e;
}
.bg-green-600 {
  background: #16a34a;
}
.bg-green-100 {
  background: #dcfce7;
}
.bg-gray-200 {
  background: #e5e7eb;
}
.bg-gray-300 {
  background: #d1d5db;
}
.rounded {
  border-radius: 0.25rem;
}
.rounded-lg {
  border-radius: 0.5rem;
}
.rounded-xl {
  border-radius: 0.75rem;
}
.rounded-2xl {
  border-radius: 1rem;
}
.shadow {
  box-shadow: 0 1px 2px 0 rgba(0,0,0,0.05), 0 1.5px 6px 0 rgba(0,0,0,0.08);
}
.hover\:shadow-md:hover {
  box-shadow: 0 4px 12px rgba(0,0,0,0.12);
}
.transition-shadow {
  transition: box-shadow 0.2s;
}
.flex {
  display: flex;
}
.flex-1 {
  flex: 1 1 0%;
}
.flex-col {
  flex-direction: column;
}
.flex-row {
  flex-direction: row;
}
.flex-wrap {
  flex-wrap: wrap;
}
.items-center {
  align-items: center;
}
.items-start {
  align-items: flex-start;
}
.justify-between {
  justify-content: space-between;
}
.gap-1 > * + * {
  margin-left: 0.25rem;
}
.gap-2 > * + * {
  margin-left: 0.5rem;
}
.gap-3 > * + * {
  margin-left: 0.75rem;
}
.gap-4 > * + * {
  margin-left: 1rem;
}
.gap-6 > * + * {
  margin-left: 1.5rem;
}
.grid {
  display: grid;
}
.grid-cols-1 {
  grid-template-columns: repeat(1, minmax(0, 1fr));
}
.grid-cols-3 {
  grid-template-columns: repeat(3, minmax(0, 1fr));
}
.grid-cols-4 {
  grid-template-columns: repeat(4, minmax(0, 1fr));
}
.lg\:grid-cols-3 {
  grid-template-columns: repeat(3, minmax(0, 1fr));
}
.lg\:col-span-1 {
  grid-column: span 1 / span 1;
}
.lg\:col-span-2 {
  grid-column: span 2 / span 2;
}
.overflow-hidden {
  overflow: hidden;
}
.w-full {
  width: 100%;
}
.h-full {
  height: 100%;
}
.h-3 {
  height: 0.75rem;
}
.h-14 {
  height: 3.5rem;
}
.h-24 {
  height: 6rem;
}
.w-4 {
  width: 1rem;
}
.h-4 {
  height: 1rem;
}
.w-5 {
  width: 1.25rem;
}
.h-5 {
  height: 1.25rem;
}
.rounded-full {
  border-radius: 9999px;
}
.inline-block {
  display: inline-block;
}
.border {
  border: 1px solid #e5e7eb;
}
.border-t {
  border-top: 1px solid #e5e7eb;
}
.px-4 {
  padding-left: 1rem; padding-right: 1rem;
}
.px-12 {
  padding-left: 3rem; padding-right: 3rem;
}
.py-1 {
  padding-top: 0.25rem; padding-bottom: 0.25rem;
}
.py-2 {
  padding-top: 0.5rem; padding-bottom: 0.5rem;
}
.py-3 {
  padding-top: 0.75rem; padding-bottom: 0.75rem;
}
.text-lg {
  font-size: 1.125rem;
}
.min-w-\[140px\] {
  min-width: 140px;
}
.min-w-\[40px\] {
  min-width: 40px;
}
.cursor-pointer {
  cursor: pointer;
}
.group:hover .group-hover\:opacity-90 {
  opacity: 0.9;
}
.bg-transparent {
  background: transparent;
}

/* Utility for hover */
.hover\:bg-green-600:hover {
  background: #16a34a;
}
.hover\:underline:hover {
  text-decoration: underline;
}
.hover\:text-gray-800:hover {
  color: #1f2937;
}

/* Responsive (chỉ dùng cho demo, nên dùng media query thực tế) */
@media (min-width: 1024px) {
  .lg\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }
  .lg\:col-span-1 {
    grid-column: span 1 / span 1;
  }
  .lg\:col-span-2 {
    grid-column: span 2 / span 2;
  }
}

/* SVG icon fill */
.fill-yellow-400 {
  fill: #facc15;
}
.text-yellow-400 {
  color: #facc15;
}
.text-gray-300 {
  color: #d1d5db;
}

</style>