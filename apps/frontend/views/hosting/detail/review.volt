<div class="col l-12 mc-12 c-12">
  <div class="customer-reviews">
    <div class="customer-reviews__container">
      <div class="customer-reviews__card">
        <div class="customer-reviews__header box-home__title">
          <h2><PERSON><PERSON><PERSON> gi<PERSON> từ khách hàng</h2>
        </div>

        <div class="customer-reviews__content">
          <div class="rating-overview">
            <div class="rating-overview__main">
              <div class="rating-overview__score">
                {{ hosting.rating ? hosting.rating.overall_avg : 0 }}
              </div>
              <div class="rating-overview__details">
                <div class="rating-stars">
                  {% set overallRating = hosting.rating ? hosting.rating.overall_avg : 0 %}
                  {% set fullStars = overallRating ? floor(overallRating) : 0 %}
                  {% set halfStar = (overallRating and (overallRating - fullStars) >= 0.25 and (overallRating - fullStars) < 0.75) ? 1 : 0 %}
                  {% set emptyStars = 5 - fullStars - halfStar %}
                  {% for i in 1..fullStars %}
                    <svg class="rating-stars__star rating-stars__star--filled" viewBox="0 0 24 24">
                      <polygon points="12,2 15.3,8.2 22,9.3 17,14.2 18.2,21 12,17.8 5.8,21 7,14.2 2,9.3 8.7,8.2"/>
                    </svg>
                  {% endfor %}
                  {% if halfStar %}
                    <svg class="rating-stars__star rating-stars__star--partial" viewBox="0 0 24 24">
                      <defs>
                        <linearGradient id="half-grad-main">
                          <stop offset="50%" stop-color="#FFD700"/>
                          <stop offset="50%" stop-color="#E0E0E0"/>
                        </linearGradient>
                      </defs>
                      <polygon fill="url(#half-grad-main)" points="12,2 15.3,8.2 22,9.3 17,14.2 18.2,21 12,17.8 5.8,21 7,14.2 2,9.3 8.7,8.2"/>
                    </svg>
                  {% endif %}
                  {% for i in 1..emptyStars %}
                    <svg class="rating-stars__star rating-stars__star--empty" viewBox="0 0 24 24">
                      <polygon points="12,2 15.3,8.2 22,9.3 17,14.2 18.2,21 12,17.8 5.8,21 7,14.2 2,9.3 8.7,8.2"/>
                    </svg>
                  {% endfor %}
                </div>
                <p class="rating-overview__count">
                  Dựa trên {{ hosting.rating ? hosting.rating.total_reviews : 0 }} đánh giá
                </p>
              </div>
            </div>
            <div class="rating-overview__summary">
              {% set overallRating = hosting.rating ? hosting.rating.overall_avg : 0 %}
              <p class="rating-overview__label">
                {% if overallRating >= 4.5 %}
                  Xuất sắc
                {% elseif overallRating >= 3.5 %}
                  Tốt
                {% elseif overallRating > 0 %}
                  Trung bình
                {% else %}
                  Chưa có đánh giá
                {% endif %}
              </p>
              <p class="rating-overview__description">Đánh giá tổng thể</p>
            </div>
          </div>

          <div class="customer-reviews__separator"></div>

          <!-- Metrics Section -->
          <div class="rating-breakdown">
            <h3 class="rating-breakdown__title">Chi tiết đánh giá</h3>
            <div class="rating-breakdown__list">
              <!-- Sạch sẽ -->
              <div class="rating-metric">
                <div class="rating-metric__label">Sạch sẽ</div>
                <div class="rating-metric__progress">
                  <div class="rating-metric__bar">
                    <div class="rating-metric__fill" style="width: {{ Ohi.calcPercent(hosting.rating ? hosting.rating.clean_avg : 0, 5) }}%"></div>
                  </div>
                </div>
                <div class="rating-metric__score">
                  <svg class="rating-metric__star" viewBox="0 0 24 24">
                    <polygon points="12,2 15.3,8.2 22,9.3 17,14.2 18.2,21 12,17.8 5.8,21 7,14.2 2,9.3 8.7,8.2"/>
                  </svg>
                  <span class="rating-metric__value">
                    {{ hosting.rating ? hosting.rating.clean_avg : 0 }}
                  </span>
                </div>
              </div>

              <!-- Nhân viên -->
              <div class="rating-metric">
                <div class="rating-metric__label">Nhân viên</div>
                <div class="rating-metric__progress">
                  <div class="rating-metric__bar">
                    <div class="rating-metric__fill" style="width: {{ Ohi.calcPercent(hosting.rating ? hosting.rating.staff_avg : 0, 5) }}%"></div>
                  </div>
                </div>
                <div class="rating-metric__score">
                  <svg class="rating-metric__star" viewBox="0 0 24 24">
                    <polygon points="12,2 15.3,8.2 22,9.3 17,14.2 18.2,21 12,17.8 5.8,21 7,14.2 2,9.3 8.7,8.2"/>
                  </svg>
                  <span class="rating-metric__value">
                    {{ hosting.rating ? hosting.rating.staff_avg : 0 }}
                  </span>
                </div>
              </div>

              <!-- Giá trị -->
              <div class="rating-metric">
                <div class="rating-metric__label">Giá trị</div>
                <div class="rating-metric__progress">
                  <div class="rating-metric__bar">
                    <div class="rating-metric__fill" style="width: {{ Ohi.calcPercent(hosting.rating ? hosting.rating.value_avg : 0, 5) }}%"></div>
                  </div>
                </div>
                <div class="rating-metric__score">
                  <svg class="rating-metric__star" viewBox="0 0 24 24">
                    <polygon points="12,2 15.3,8.2 22,9.3 17,14.2 18.2,21 12,17.8 5.8,21 7,14.2 2,9.3 8.7,8.2"/>
                  </svg>
                  <span class="rating-metric__value">
                    {{ hosting.rating ? hosting.rating.value_avg : 0 }}
                  </span>
                </div>
              </div>

              <!-- Vị trí -->
              <div class="rating-metric">
                <div class="rating-metric__label">Vị trí</div>
                <div class="rating-metric__progress">
                  <div class="rating-metric__bar">
                    <div class="rating-metric__fill" style="width: {{ Ohi.calcPercent(hosting.rating ? hosting.rating.location_avg : 0, 5) }}%"></div>
                  </div>
                </div>
                <div class="rating-metric__score">
                  <svg class="rating-metric__star" viewBox="0 0 24 24">
                    <polygon points="12,2 15.3,8.2 22,9.3 17,14.2 18.2,21 12,17.8 5.8,21 7,14.2 2,9.3 8.7,8.2"/>
                  </svg>
                  <span class="rating-metric__value">
                    {{ hosting.rating ? hosting.rating.location_avg : 0 }}
                  </span>
                </div>
              </div>

              <!-- Tiện nghi -->
              <div class="rating-metric">
                <div class="rating-metric__label">Tiện nghi</div>
                <div class="rating-metric__progress">
                  <div class="rating-metric__bar">
                    <div class="rating-metric__fill" style="width: {{ Ohi.calcPercent(hosting.rating ? hosting.rating.amenities_avg : 0, 5) }}%"></div>
                  </div>
                </div>
                <div class="rating-metric__score">
                  <svg class="rating-metric__star" viewBox="0 0 24 24">
                    <polygon points="12,2 15.3,8.2 22,9.3 17,14.2 18.2,21 12,17.8 5.8,21 7,14.2 2,9.3 8.7,8.2"/>
                  </svg>
                  <span class="rating-metric__value">
                    {{ hosting.rating ? hosting.rating.amenities_avg : 0 }}
                  </span>
                </div>
              </div>
            </div>
          </div>

          <div class="customer-reviews__separator"></div>

          {% if hosting_reviews is defined and hosting_reviews|length > 0 %}
          <div class="reviews-section">
            <h3 class="reviews-section__title">Đánh giá gần đây</h3>
            <div class="reviews-list" id="reviews-list">
              {% for review in hosting_reviews %}
                <div class="review-item" {% if loop.index > 10 %}style="display: none;" data-hidden="true"{% endif %}>
                  <div class="review-item__layout">
                    <div class="review-item__content">
                      <div class="review-item__header">
                        <div class="review-item__user">
                          <div class="review-item__avatar">
                            <div class="review-avatar">
                              {% if review.booking and review.booking.user and review.booking.user.avatar %}
                                <img src="{{ review.booking.user.avatar }}" alt="{{ review.booking.user_fullname }}" class="review-avatar__image" />
                              {% else %}
                                <div class="review-avatar__fallback">
                                  {{ review.getAvatarWithName() }}
                                </div>
                              {% endif %}
                            </div>
                            <div class="review-item__avatar--info">
                              <p class="review-item__name">
                                {{ review.booking ? review.booking.user_fullname : 'Khách hàng' }}
                              </p>
                              {% if review.traveler_type and review.traveler_type|trim != '' %}
                                <div class="review-item__traveler-type">
                                  <span class="review-item__label">Loại khách:</span>
                                  <span class="review-item__value">
                                    {{ review.getTravelerTypeLabel() }}
                                  </span>
                                </div>
                              {% endif %}
                            </div>
                          </div>
                          <div class="review-item__meta">
                            <div class="rating-stars rating-stars--small">
                              {% set reviewRating = review.overall ? review.overall : 0 %}
                              {% set fullStars = reviewRating ? floor(reviewRating) : 0 %}
                              {% set halfStar = (reviewRating and (reviewRating - fullStars) >= 0.25 and (reviewRating - fullStars) < 0.75) ? 1 : 0 %}
                              {% set emptyStars = 5 - fullStars - halfStar %}

                              {% for i in 1..fullStars %}
                                <svg class="rating-stars__star rating-stars__star--filled" viewBox="0 0 24 24">
                                  <polygon points="12,2 15.3,8.2 22,9.3 17,14.2 18.2,21 12,17.8 5.8,21 7,14.2 2,9.3 8.7,8.2"/>
                                </svg>
                              {% endfor %}

                              {% if halfStar %}
                                <svg class="rating-stars__star rating-stars__star--partial" viewBox="0 0 24 24">
                                  <defs>
                                    <linearGradient id="half-grad-review-{{ loop.index }}">
                                      <stop offset="50%" stop-color="#FFD700"/>
                                      <stop offset="50%" stop-color="#E0E0E0"/>
                                    </linearGradient>
                                  </defs>
                                  <polygon fill="url(#half-grad-review-{{ loop.index }})" points="12,2 15.3,8.2 22,9.3 17,14.2 18.2,21 12,17.8 5.8,21 7,14.2 2,9.3 8.7,8.2"/>
                                </svg>
                              {% endif %}

                              {% for i in 1..emptyStars %}
                                <svg class="rating-stars__star rating-stars__star--empty" viewBox="0 0 24 24">
                                  <polygon points="12,2 15.3,8.2 22,9.3 17,14.2 18.2,21 12,17.8 5.8,21 7,14.2 2,9.3 8.7,8.2"/>
                                </svg>
                              {% endfor %}
                            </div>
                            <span class="review-item__date">
                              {{ Ohi.convertDateToTimeAgo(review.created) }}
                            </span>
                          </div>
                        </div>
                      </div>

                      {% if review.title and review.title|trim != '' %}
                        <h4 class="review-item__title">{{ review.title }}</h4>
                      {% endif %}

                      <div class="review-item__text-container">
                        <p class="review-item__text review-item__text--truncated" data-full-text="{{ review.content }}">
                          {{ review.content }}
                        </p>
                        {% if review.content|length > 150 %}
                          <span class="review-item__read-more" data-action="expand">Xem thêm</span>
                        {% endif %}
                      </div>

                      {% if review.imgs %}
                        {% set reviewImages = review.imgs|json_decode %}
                        {% if reviewImages and reviewImages|length > 0 %}
                          <div class="review-images" data-review-id="{{ review.id }}">
                            {% for img in reviewImages %}
                              {% if loop.index <= 8 %}
                                <div class="review-images__item-wrapper" data-index="{{ loop.index - 1 }}">
                                  <img src="{{ url(img) }}" alt="Hình ảnh đánh giá {{ loop.index }}" class="review-images__item review-images__link" />
                                  {% if loop.index == 8 and reviewImages|length > 8 %}
                                    <div class="review-images__overlay">
                                      <span class="review-images__more">+{{ reviewImages|length - 8 }}</span>
                                    </div>
                                  {% endif %}
                                </div>
                              {% endif %}
                            {% endfor %}
                          </div>
                        {% endif %}
                      {% endif %}
                    </div>
                  </div>
                </div>
                {% if not loop.last and loop.index <= 10 %}
                  <div class="customer-reviews__separator"></div>
                {% endif %}
              {% endfor %}
            </div>

            {% if hosting_reviews|length > 10 %}
              <div class="load-more-reviews">
                <button class="load-more-reviews__btn" id="load-more-reviews-btn" data-hosting-id="{{ hosting.id }}" data-page="2">
                  Xem thêm đánh giá
                </button>
                <div class="load-more-reviews__loading" id="load-more-loading">
                  <div class="load-more-reviews__spinner"></div>
                  <span>Đang tải...</span>
                </div>
              </div>
            {% endif %}
          </div>
          {% else %}
            <div class="reviews-section">
              <h3 class="reviews-section__title">Đánh giá gần đây</h3>
              <p class="review-item__text">Chưa có đánh giá nào cho chỗ ở này.</p>
            </div>
          {% endif %}
        </div>
      </div>
    </div>
  </div>
</div>