<!-- Single Review Item Template for AJAX loading -->
<div class="review-item">
  <div class="review-item__layout">
    <div class="review-item__content">
      <div class="review-item__header">
        <div class="review-item__user">
          <div class="review-item__avatar">
            <div class="review-avatar">
              {% if review.booking and review.booking.user and review.booking.user.avatar %}
                <img src="{{ review.booking.user.avatar }}" alt="{{ review.booking.user_fullname }}" class="review-avatar__image" />
              {% else %}
                <div class="review-avatar__fallback">
                  {{ review.getAvatarWithName() }}
                </div>
              {% endif %}
            </div>
            <div class="review-item__avatar--info">
              <p class="review-item__name">
                {{ review.booking ? review.booking.user_fullname : 'Khách hàng' }}
              </p>
              {% if review.traveler_type and review.traveler_type|trim != '' %}
                <div class="review-item__traveler-type">
                  <span class="review-item__label">Lo<PERSON><PERSON> khách:</span>
                  <span class="review-item__value">
                    {{ review.getTravelerTypeLabel() }}
                  </span>
                </div>
              {% endif %}
            </div>
          </div>
          <div class="review-item__meta">
            <div class="rating-stars rating-stars--small">
              {% set reviewRating = review.overall ? review.overall : 0 %}
              {% set fullStars = reviewRating ? floor(reviewRating) : 0 %}
              {% set halfStar = (reviewRating and (reviewRating - fullStars) >= 0.25 and (reviewRating - fullStars) < 0.75) ? 1 : 0 %}
              {% set emptyStars = 5 - fullStars - halfStar %}

              {% for i in 1..fullStars %}
                <svg class="rating-stars__star rating-stars__star--filled" viewBox="0 0 24 24">
                  <polygon points="12,2 15.3,8.2 22,9.3 17,14.2 18.2,21 12,17.8 5.8,21 7,14.2 2,9.3 8.7,8.2"/>
                </svg>
              {% endfor %}

              {% if halfStar %}
                <svg class="rating-stars__star rating-stars__star--partial" viewBox="0 0 24 24">
                  <defs>
                    <linearGradient id="half-grad-review-ajax-{{ random() }}">
                      <stop offset="50%" stop-color="#FFD700"/>
                      <stop offset="50%" stop-color="#E0E0E0"/>
                    </linearGradient>
                  </defs>
                  <polygon fill="url(#half-grad-review-ajax-{{ random() }})" points="12,2 15.3,8.2 22,9.3 17,14.2 18.2,21 12,17.8 5.8,21 7,14.2 2,9.3 8.7,8.2"/>
                </svg>
              {% endif %}

              {% for i in 1..emptyStars %}
                <svg class="rating-stars__star rating-stars__star--empty" viewBox="0 0 24 24">
                  <polygon points="12,2 15.3,8.2 22,9.3 17,14.2 18.2,21 12,17.8 5.8,21 7,14.2 2,9.3 8.7,8.2"/>
                </svg>
              {% endfor %}
            </div>
            <span class="review-item__date">
              {{ Ohi.convertDateToTimeAgo(review.created) }}
            </span>
          </div>
        </div>
      </div>

      {% if review.title and review.title|trim != '' %}
        <h4 class="review-item__title">{{ review.title }}</h4>
      {% endif %}

      <div class="review-item__text-container">
        <p class="review-item__text review-item__text--truncated" data-full-text="{{ review.content }}">
          {{ review.content }}
        </p>
        {% if review.content|length > 150 %}
          <span class="review-item__read-more" data-action="expand">Xem thêm</span>
        {% endif %}
      </div>

      {% if review.imgs %}
        {% set reviewImages = review.imgs|json_decode %}
        {% if reviewImages and reviewImages|length > 0 %}
          <div class="review-images">
            {% for img in reviewImages %}
              <img src="{{ url(img) }}" alt="Hình ảnh đánh giá" class="review-images__item" />
            {% endfor %}
          </div>
        {% endif %}
      {% endif %}
    </div>
  </div>
</div>
<div class="customer-reviews__separator"></div>
