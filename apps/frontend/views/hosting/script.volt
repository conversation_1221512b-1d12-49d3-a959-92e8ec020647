<script>
  $(document).ready(function () {
    randomRate();
    initVideoObserver();
    $('.box-form__booking .item-qty').on('click', '.variation', (e) => Common.onVariationQnt(e));

    $('.gallery__item--link').click(function (e) {
      e.preventDefault();
      const uri = "{{ url('/hosting/hostingViewImgs') }}";
      const hostingId = $(this).data('id');
      const index = $(this).data('index') - 1;
      $.post({
        url: uri,
        data: { hostingId: hostingId },
        success: resp => {
          if (resp && resp.toString().trim() !== '') {
            const respObj = JSON.parse(resp);
            if (Array.isArray(respObj)) {
              const baseUrl = "{{ url() }}";
              const fancyboxData = respObj.map(url => ({
                src: baseUrl + url,
                thumb: baseUrl + url,
                opts: {
                  thumb: baseUrl + url
                },
              }));
              if (index >= 0 && index < fancyboxData.length) {
                Fancybox.show(fancyboxData, {
                  startIndex: index
                });
              } else {
                console.log("Index không hợp lệ");
              }
            } else {
              console.log("RespObj is not an array");
            }
          } else {
            alert('Không có dữ liệu');
          }
        }
      }, 'json');
    });


    function randomRate() {
      let rate = Math.random() * (5 - 4) + 4;
      rate = rate.toFixed(1);
      $('.item-rate.rate span').text(rate);
    }

    function initVideoObserver() {
      const observer = new IntersectionObserver((entries, observer) => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            loadVideoFormData(entry.target);
            observer.unobserve(entry.target);
          }
        });
      });

      const $videoContainer = $('.box-home__review .video');

      if ($videoContainer.length) {
        observer.observe($videoContainer[0]);
      }
    }

    async function loadVideoFormData(element) {
      try {
        $element = $(element);
        const url = $element.attr('rel');
        const data = await loadDataFromURL(url);
        const html = `<iframe width="100%" height="640" src="${
          "https://www.tiktok.com/player/v1/" + data.embed_product_id
        }" allow="fullscreen" title="test"></iframe>`;
        $element.append(html);
        $element.find('img').hide();
      } catch (error) {
        console.error('Có lỗi xảy ra khi tải dữ liệu video:', error);
      }
    }

  });

  $(document).ready(function () {
    // Target the container that holds the content
    var contentContainer = $('.content-detail');
    var contentElements = contentContainer.children(); // Get all direct children elements

    // Original height
    var originalHeight = contentContainer.height();

    // Setup for checking height
    var currentHeight = 0;
    var visibleElements = 0;
    var maxHeight = 150; // Maximum height in pixels

    // Check which elements should be visible
    contentElements.each(function (index) {
      $(this).css('margin-bottom', '15px'); // Add consistent spacing
      currentHeight += $(this).outerHeight(true);
      
      if (currentHeight <= maxHeight) {
        visibleElements++;
      }
    });

    // Ensure we show at least one element even if it exceeds the height
    visibleElements = Math.max(visibleElements, 1);

    // If we need to truncate content
    if (visibleElements < contentElements.length) {
      // Wrap remaining elements in a hidden div
      contentElements.slice(visibleElements).wrapAll('<div class="hidden-content" style="display: none;"></div>');

      // Add the "Read more" button
      contentContainer.append('<div class="read-more-btn"><a href="#" class="read-more">Đọc thêm</a></div>');

      // Handle click events
      $('.read-more').click(function (e) {
        e.preventDefault();

        if ($('.hidden-content').is(':visible')) {
          $('.hidden-content').hide();
          $(this).text('Đọc thêm');
        } else {
          $('.hidden-content').show();
          $(this).text('Ẩn bớt');
        }
      });
    }
  });

  var navPosition = $(".nav__fixed").offset().top;
  var navHeight = $(".nav__fixed").outerHeight();

  function stickyNav() {
    var windowHeight = $(window).scrollTop();
    var navFixed = $(".nav__fixed");

    if (navFixed.length) {
        if (windowHeight > navPosition) {
            navFixed.addClass("active");
            $('body').css('padding-top', navHeight); // Thêm padding để tránh giật layout
        } else {
            navFixed.removeClass("active");
            $('body').css('padding-top', 0);
        }
    }
  }

  $(document).on('click', '.nav__fixed .link_fixed', function (e) {
    e.preventDefault();
    const href = $(this).attr('href');
    const target = $(href);
    
    if (target.length) {
        const targetPosition = target.offset().top;
        // Kiểm tra xem vị trí đích có làm nav trở thành sticky không
        const willNavBeSticky = targetPosition > navPosition;
        const header = $('header').outerHeight();
        
        const offset = targetPosition - (willNavBeSticky ? (navHeight + header + 20) : (header + 20));
        
        $('html, body').animate({
            scrollTop: offset
        }, {
            duration: 1000,
            queue: false
        });
    }
  });

  var scrollTimeout;
  $(window).on('scroll', function() {
      if (scrollTimeout) {
          clearTimeout(scrollTimeout);
      }
      scrollTimeout = setTimeout(function() {
          stickyNav();
      }, 100);
  });

  $(document).on('click', '.showDetailRoom', function (e) {
    e.preventDefault();
    const roomId = $(this).data('id');
    const uri = "{{ url('/hosting/getDetailHostingRoom') }}"

    $.post({
      url: uri,
      dataType: 'json',
      data: { roomId: roomId },
      success: resp => {
        if (resp && resp.status == true && resp.result) {
          $('#popupRoomDetail').find('.popup-inner__wrap').html(resp.result);
          $('.room__gallery--cont').slick({
            slidesToShow: 1,
            slidesToScroll: 1,
            arrows: false,
            autoPlay: 1000,
            asNavFor: '.room__gallery--nav'
          });
          $('.room__gallery--nav').slick({
            slidesToShow: 5,
            slidesToScroll: 1,
            asNavFor: '.room__gallery--cont',
            centerMode: true,
            focusOnSelect: true,
            arrows: false,
            centerPadding: 0
          });
          Common.onOpenPopup('#popupRoomDetail');
        } else {
          alert('Có lỗi xảy ra');
        }
      }
    });
  });

  $(document).on('click', '[data-chatlio]', function (e) {
    e.preventDefault();
    window.PancakeChatPlugin.openChatBox()
    const content = $(this).data('chatlio');
    const chatTextArea = $('.pkcp-base-input.setup-message-area');
    const chatInput = $('#pkcp-popup-conversation-box');
    if (chatTextArea) {
      chatTextArea.val(content);
    }
    if (chatInput) {
      chatInput.val(content);
    }
  });

  $(document).on('click', '.showDetailVilla', function (e) {
    e.preventDefault();
    const villaId = $(this).data('id');
    const uri = "{{ url('/hosting/getDetailHostingVilla') }}"

    $.post({
      url: uri,
      dataType: 'json',
      data: { villaId: villaId },
      success: resp => {
        if (resp && resp.status == true && resp.result) {
          $('#popupVillaDetail').find('.popup-inner__wrap').html(resp.result);
          Common.onOpenPopup('#popupVillaDetail');
        } else {
          alert('Có lỗi xảy ra');
        }
      }
    })
  })

  $('#popupRoomDetail').on('click', '.cancel', function (e) {
    e.preventDefault();
    Common.onClosePopup();
  });

  $(document).ready(function () {
    Booking.values = {
      checkInDate: '{{ bookingInfo['startDate'] is not empty ? date("Y-m-d", strtotime(bookingInfo['startDate'])) : date("Y-m-d", strtotime('+1 day')) }}',
      checkOutDate: '{{ bookingInfo['endDate'] is not empty ? date("Y-m-d", strtotime(bookingInfo['endDate'])) : date("Y-m-d", strtotime('+2 day')) }}',
      adult: '{{ bookingInfo['adult'] is not empty ? bookingInfo['adult'] : 2 }}',
      child: '{{ bookingInfo['child'] is not empty ? bookingInfo['child'] : null }}',
      childrenDetail: '{{ bookingInfo['childrenDetail'] is not empty ? bookingInfo['childrenDetail']|json_encode : null }}'
    };

    Booking.elements = {
      bookingDateTime:  () => $("input[name='booking-datetime']"),
      bookingAdult:     () => $("input[name='booking-adult']"),
      bookingChild:     () => $("input[name='booking-child']"),
      bookingQnt:       () => $("input[name='quantity[]']"),
      btnCheckAvail:    () => $("#btn-check-avail"),
      btnBooking:       () => $("#btn-booking"),
      bookingGuest:     () => $('.box-form__booking .dropdown'),
      hostingId:        () => $("input[name='hosting_id']"),
      quantity:         () => $("input[name='quantity']"),
      variation:        () => $("input[name='quantity']").parent().find('.variation'),
      selectAge:        () => $('.dropdown-children .select-age'),
      rentalType:       () => $('input[name="booking-rental"]'),
    };

    $('.rental-type .dropdown-item').click(function (e) {
      e.preventDefault();
      Booking.services.onChangeRentalType(e);
    });
    
  });

  $(document).ready(() => {
    Booking.events();
  });
</script>