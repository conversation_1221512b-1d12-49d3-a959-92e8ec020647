<script>
  $(document).ready(function () {
    randomRate();
    initVideoObserver();
    $('.box-form__booking .item-qty').on('click', '.variation', (e) => Common.onVariationQnt(e));

    $('.gallery__item--link').click(function (e) {
      e.preventDefault();
      const uri = "{{ url('/hosting/hostingViewImgs') }}";
      const hostingId = $(this).data('id');
      const index = $(this).data('index') - 1;
      $.post({
        url: uri,
        data: { hostingId: hostingId },
        success: resp => {
          if (resp && resp.toString().trim() !== '') {
            const respObj = JSON.parse(resp);
            if (Array.isArray(respObj)) {
              const baseUrl = "{{ url() }}";
              const fancyboxData = respObj.map(url => ({
                src: baseUrl + url,
                thumb: baseUrl + url,
                opts: {
                  thumb: baseUrl + url
                },
              }));
              if (index >= 0 && index < fancyboxData.length) {
                Fancybox.show(fancyboxData, {
                  startIndex: index
                });
              } else {
                console.log("Index không hợp lệ");
              }
            } else {
              console.log("RespObj is not an array");
            }
          } else {
            alert('Không có dữ liệu');
          }
        }
      }, 'json');
    });


    function randomRate() {
      let rate = Math.random() * (5 - 4) + 4;
      rate = rate.toFixed(1);
      $('.item-rate.rate span').text(rate);
    }

    function initVideoObserver() {
      const observer = new IntersectionObserver((entries, observer) => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            loadVideoFormData(entry.target);
            observer.unobserve(entry.target);
          }
        });
      });

      const $videoContainer = $('.box-home__review .video');

      if ($videoContainer.length) {
        observer.observe($videoContainer[0]);
      }
    }

    async function loadVideoFormData(element) {
      try {
        $element = $(element);
        const url = $element.attr('rel');
        const data = await loadDataFromURL(url);
        const html = `<iframe width="100%" height="640" src="${
          "https://www.tiktok.com/player/v1/" + data.embed_product_id
        }" allow="fullscreen" title="test"></iframe>`;
        $element.append(html);
        $element.find('img').hide();
      } catch (error) {
        console.error('Có lỗi xảy ra khi tải dữ liệu video:', error);
      }
    }

  });

  $(document).ready(function () {
    // Target the container that holds the content
    var contentContainer = $('.content-detail');
    var contentElements = contentContainer.children(); // Get all direct children elements

    // Original height
    var originalHeight = contentContainer.height();

    // Setup for checking height
    var currentHeight = 0;
    var visibleElements = 0;
    var maxHeight = 150; // Maximum height in pixels

    // Check which elements should be visible
    contentElements.each(function (index) {
      $(this).css('margin-bottom', '15px'); // Add consistent spacing
      currentHeight += $(this).outerHeight(true);
      
      if (currentHeight <= maxHeight) {
        visibleElements++;
      }
    });

    // Ensure we show at least one element even if it exceeds the height
    visibleElements = Math.max(visibleElements, 1);

    // If we need to truncate content
    if (visibleElements < contentElements.length) {
      // Wrap remaining elements in a hidden div
      contentElements.slice(visibleElements).wrapAll('<div class="hidden-content" style="display: none;"></div>');

      // Add the "Read more" button
      contentContainer.append('<div class="read-more-btn"><a href="#" class="read-more">Đọc thêm</a></div>');

      // Handle click events
      $('.read-more').click(function (e) {
        e.preventDefault();

        if ($('.hidden-content').is(':visible')) {
          $('.hidden-content').hide();
          $(this).text('Đọc thêm');
        } else {
          $('.hidden-content').show();
          $(this).text('Ẩn bớt');
        }
      });
    }
  });

  var navPosition = $(".nav__fixed").offset().top;
  var navHeight = $(".nav__fixed").outerHeight();

  function stickyNav() {
    var windowHeight = $(window).scrollTop();
    var navFixed = $(".nav__fixed");

    if (navFixed.length) {
        if (windowHeight > navPosition) {
            navFixed.addClass("active");
            $('body').css('padding-top', navHeight); // Thêm padding để tránh giật layout
        } else {
            navFixed.removeClass("active");
            $('body').css('padding-top', 0);
        }
    }
  }

  $(document).on('click', '.nav__fixed .link_fixed', function (e) {
    e.preventDefault();
    const href = $(this).attr('href');
    const target = $(href);
    
    if (target.length) {
        const targetPosition = target.offset().top;
        // Kiểm tra xem vị trí đích có làm nav trở thành sticky không
        const willNavBeSticky = targetPosition > navPosition;
        const header = $('header').outerHeight();
        
        const offset = targetPosition - (willNavBeSticky ? (navHeight + header + 20) : (header + 20));
        
        $('html, body').animate({
            scrollTop: offset
        }, {
            duration: 1000,
            queue: false
        });
    }
  });

  var scrollTimeout;
  $(window).on('scroll', function() {
      if (scrollTimeout) {
          clearTimeout(scrollTimeout);
      }
      scrollTimeout = setTimeout(function() {
          stickyNav();
      }, 100);
  });

  $(document).on('click', '.showDetailRoom', function (e) {
    e.preventDefault();
    const roomId = $(this).data('id');
    const uri = "{{ url('/hosting/getDetailHostingRoom') }}"

    $.post({
      url: uri,
      dataType: 'json',
      data: { roomId: roomId },
      success: resp => {
        if (resp && resp.status == true && resp.result) {
          $('#popupRoomDetail').find('.popup-inner__wrap').html(resp.result);
          $('.room__gallery--cont').slick({
            slidesToShow: 1,
            slidesToScroll: 1,
            arrows: false,
            autoPlay: 1000,
            asNavFor: '.room__gallery--nav'
          });
          $('.room__gallery--nav').slick({
            slidesToShow: 5,
            slidesToScroll: 1,
            asNavFor: '.room__gallery--cont',
            centerMode: true,
            focusOnSelect: true,
            arrows: false,
            centerPadding: 0
          });
          Common.onOpenPopup('#popupRoomDetail');
        } else {
          alert('Có lỗi xảy ra');
        }
      }
    });
  });

  $(document).on('click', '[data-chatlio]', function (e) {
    e.preventDefault();
    window.PancakeChatPlugin.openChatBox()
    const content = $(this).data('chatlio');
    const chatTextArea = $('.pkcp-base-input.setup-message-area');
    const chatInput = $('#pkcp-popup-conversation-box');
    if (chatTextArea) {
      chatTextArea.val(content);
    }
    if (chatInput) {
      chatInput.val(content);
    }
  });

  $(document).on('click', '.showDetailVilla', function (e) {
    e.preventDefault();
    const villaId = $(this).data('id');
    const uri = "{{ url('/hosting/getDetailHostingVilla') }}"

    $.post({
      url: uri,
      dataType: 'json',
      data: { villaId: villaId },
      success: resp => {
        if (resp && resp.status == true && resp.result) {
          $('#popupVillaDetail').find('.popup-inner__wrap').html(resp.result);
          Common.onOpenPopup('#popupVillaDetail');
        } else {
          alert('Có lỗi xảy ra');
        }
      }
    })
  })

  $('#popupRoomDetail').on('click', '.cancel', function (e) {
    e.preventDefault();
    Common.onClosePopup();
  });

  $(document).ready(function () {
    Booking.values = {
      checkInDate: '{{ bookingInfo['startDate'] is not empty ? date("Y-m-d", strtotime(bookingInfo['startDate'])) : date("Y-m-d", strtotime('+1 day')) }}',
      checkOutDate: '{{ bookingInfo['endDate'] is not empty ? date("Y-m-d", strtotime(bookingInfo['endDate'])) : date("Y-m-d", strtotime('+2 day')) }}',
      adult: '{{ bookingInfo['adult'] is not empty ? bookingInfo['adult'] : 2 }}',
      child: '{{ bookingInfo['child'] is not empty ? bookingInfo['child'] : null }}',
      childrenDetail: '{{ bookingInfo['childrenDetail'] is not empty ? bookingInfo['childrenDetail']|json_encode : null }}'
    };

    Booking.elements = {
      bookingDateTime:  () => $("input[name='booking-datetime']"),
      bookingAdult:     () => $("input[name='booking-adult']"),
      bookingChild:     () => $("input[name='booking-child']"),
      bookingQnt:       () => $("input[name='quantity[]']"),
      btnCheckAvail:    () => $("#btn-check-avail"),
      btnBooking:       () => $("#btn-booking"),
      bookingGuest:     () => $('.box-form__booking .dropdown'),
      hostingId:        () => $("input[name='hosting_id']"),
      quantity:         () => $("input[name='quantity']"),
      variation:        () => $("input[name='quantity']").parent().find('.variation'),
      selectAge:        () => $('.dropdown-children .select-age'),
      rentalType:       () => $('input[name="booking-rental"]'),
    };

    $('.rental-type .dropdown-item').click(function (e) {
      e.preventDefault();
      Booking.services.onChangeRentalType(e);
    });
    
  });

  $(document).ready(() => {
    Booking.events();

    // Customer Reviews functionality
    initCustomerReviews();
  });

  function initCustomerReviews() {
    // Text truncation functionality
    $(document).on('click', '.review-item__read-more', function(e) {
      e.preventDefault();
      const $this = $(this);
      const $textElement = $this.siblings('.review-item__text');
      const fullText = $textElement.data('full-text');
      const action = $this.data('action');

      if (action === 'expand') {
        $textElement.removeClass('review-item__text--truncated').addClass('review-item__text--expanded');
        $textElement.text(fullText);
        $this.text('Ẩn bớt').data('action', 'collapse');
      } else {
        $textElement.removeClass('review-item__text--expanded').addClass('review-item__text--truncated');
        $this.text('Xem thêm').data('action', 'expand');
      }
    });

    // Load more reviews functionality
    $(document).on('click', '#load-more-reviews-btn', function(e) {
      e.preventDefault();
      const $btn = $(this);
      const $loading = $('#load-more-loading');
      const hostingId = $btn.data('hosting-id');
      const page = $btn.data('page');

      // Show loading state
      $btn.hide();
      $loading.addClass('active');

      $.ajax({
        url: "{{ url('/hosting/loadMoreReviews') }}",
        type: 'POST',
        dataType: 'json',
        data: {
          hosting_id: hostingId,
          page: page
        },
        success: function(response) {
          if (response.status && response.data) {
            // Append new reviews to the list
            $('#reviews-list').append(response.data);

            // Update page number for next request
            $btn.data('page', page + 1);

            // Show/hide load more button based on hasMore
            if (response.hasMore) {
              $btn.show();
            } else {
              $btn.remove();
            }
          } else {
            alert('Có lỗi xảy ra khi tải đánh giá');
            $btn.show();
          }
        },
        error: function() {
          alert('Có lỗi xảy ra khi tải đánh giá');
          $btn.show();
        },
        complete: function() {
          $loading.removeClass('active');
        }
      });
    });

    // Show hidden reviews (for initial 10+ reviews)
    let hiddenReviewsShown = false;
    $(document).on('click', '#load-more-reviews-btn', function(e) {
      if (!hiddenReviewsShown) {
        e.preventDefault();
        const $hiddenReviews = $('.review-item[data-hidden="true"]');

        if ($hiddenReviews.length > 0) {
          $hiddenReviews.show().removeAttr('data-hidden');

          // Add separators for newly shown reviews
          $hiddenReviews.each(function(index) {
            if (index < $hiddenReviews.length - 1) {
              $(this).after('<div class="customer-reviews__separator"></div>');
            }
          });

          hiddenReviewsShown = true;

          // Update button text or hide if no more reviews to load
          const $btn = $(this);
          const totalReviews = $('.review-item').length;
          const hostingId = $btn.data('hosting-id');

          // Check if there are more reviews to load from server
          $.ajax({
            url: "{{ url('/hosting/checkMoreReviews') }}",
            type: 'POST',
            dataType: 'json',
            data: {
              hosting_id: hostingId,
              current_count: totalReviews
            },
            success: function(response) {
              if (!response.hasMore) {
                $btn.hide();
              }
            }
          });
        }
      }
    });

    // Review Images Gallery functionality
    $(document).on('click', '.review-images__link', function(e) {
      e.preventDefault();
      const $wrapper = $(this).closest('.review-images__item-wrapper');
      const $gallery = $(this).closest('.review-images');
      const reviewId = $gallery.data('review-id');
      const clickedIndex = parseInt($wrapper.data('index'));

      console.log('Review ID:', reviewId, 'Clicked Index:', clickedIndex);

      // Check if Fancybox is available
      if (typeof window.Fancybox === 'undefined') {
        console.error('Fancybox is not loaded yet, waiting...');
        // Wait for Fancybox to load
        const checkFancybox = setInterval(() => {
          if (typeof window.Fancybox !== 'undefined') {
            clearInterval(checkFancybox);
            loadReviewGallery(reviewId, clickedIndex);
          }
        }, 100);
        return;
      }

      loadReviewGallery(reviewId, clickedIndex);
    });

    // Function to load review gallery
    function loadReviewGallery(reviewId, clickedIndex) {
      // Get review images via AJAX
      $.ajax({
        url: "{{ url('/hosting/getReviewImages') }}",
        type: 'POST',
        data: { reviewId: reviewId },
        dataType: 'json',
        success: function(response) {
          console.log('AJAX Response:', response);

          if (response && response.status === true && response.images) {
            let images = response.images;

            // Ensure images is an array
            if (!Array.isArray(images)) {
              console.error('Images is not an array:', images);
              alert('Dữ liệu hình ảnh không đúng định dạng');
              return;
            }

            if (images.length === 0) {
              alert('Không có hình ảnh để hiển thị');
              return;
            }

            const baseUrl = "{{ url() }}";
            const fancyboxData = [];

            // Build fancybox data array
            images.forEach((url, index) => {
              const fullUrl = url.startsWith('http') ? url : baseUrl + url;
              fancyboxData.push({
                src: fullUrl,
                thumb: fullUrl,
                caption: `Hình ảnh đánh giá ${index + 1}/${images.length}`
              });
            });

            console.log('Fancybox Data:', fancyboxData);
            console.log('Start Index:', clickedIndex);

            // Validate index
            const startIndex = (clickedIndex >= 0 && clickedIndex < fancyboxData.length) ? clickedIndex : 0;

            // Show Fancybox - ensure we pass an array
            try {
              if (typeof window.Fancybox !== 'undefined' && typeof window.Fancybox.show === 'function') {
                // Double check that fancyboxData is an array
                if (!Array.isArray(fancyboxData)) {
                  console.error('fancyboxData is not an array:', fancyboxData);
                  alert('Dữ liệu gallery không đúng định dạng');
                  return;
                }

                window.Fancybox.show(fancyboxData, {
                  startIndex: startIndex,
                  Toolbar: {
                    display: {
                      left: ["infobar"],
                      middle: [
                        "zoomIn",
                        "zoomOut",
                        "toggle1to1",
                        "rotateCCW",
                        "rotateCW",
                        "flipX",
                        "flipY",
                      ],
                      right: ["slideshow", "thumbs", "close"],
                    },
                  },
                  Thumbs: {
                    autoStart: false,
                  },
                });
              } else {
                console.error('Fancybox is not available');
                alert('Gallery không khả dụng');
              }
            } catch (fancyboxError) {
              console.error('Fancybox Error:', fancyboxError);
              console.error('Fancybox Data Type:', typeof fancyboxData);
              console.error('Is Array:', Array.isArray(fancyboxData));
              alert('Có lỗi khi mở gallery: ' + fancyboxError.message);
            }
          } else {
            console.error('Invalid response:', response);
            alert('Không thể tải hình ảnh: ' + (response.message || 'Unknown error'));
          }
        },
        error: function(xhr, status, error) {
          console.error('AJAX Error:', status, error);
          console.error('Response:', xhr.responseText);
          alert('Có lỗi xảy ra khi tải hình ảnh');
        }
      });
    }
  }
</script>