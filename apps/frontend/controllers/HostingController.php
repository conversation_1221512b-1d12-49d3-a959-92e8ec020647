<?php

namespace Modules\Frontend\Controllers;

use Modules\App\Models\HostingLocationDistanceModel;
use Modules\App\Models\HostingModel;
use Modules\App\Models\HostingPremiumModel;
use Modules\App\Models\HostingPropertiesGroupModel;
use Modules\App\Models\HostingPropertiesHasModel;
use Modules\App\Models\HostingRoomBedModel;
use Modules\App\Models\HostingRoomModel;
use Modules\App\Models\HostingRoomPropertiesModel;
use Modules\App\Models\HostingRulesModel;
use Modules\App\Models\HostingSavedModel;
use Modules\App\Models\HostingTypeModel;
use Modules\App\Models\HostingVillaItemModel;
use Modules\App\Models\HostingVillaModel;
use Modules\App\Models\HostingVillaPropertiesModel;
use Modules\App\Models\HostingWholeModel;
use Modules\App\Models\HostingWholeRoomModel;
use Modules\App\Models\LocationAreaGroupModel;
use Modules\App\Models\LocationDistrictModel;
use Modules\App\Models\LocationProvinceModel;
use Modules\Library\Oh\Maps;
use Phalcon\Paginator\Pager;
use Phalcon\Paginator\Adapter\Model as PaginatorModel;

class HostingController extends ControllerBase
{
  protected function onConstruct()
  {
    parent::onConstruct();
    $bookingInfo = $this->session->get('bookingInfo');
    if (!empty($bookingInfo)) {
      $this->view->setVar('bookingInfo', $bookingInfo);
    }

    $this->assets->addCss('/library/daterangepickerNew/daterangepicker.css');
    $this->assets->addJs('/library/daterangepickerNew/moment.min.js');
    $this->assets->addJs('/library/daterangepickerNew/daterangepicker.js');
    $this->view->page = 'home';
  }

  public function indexAction($type_code = null, $code = null)
  {
    if (!empty($code)) {
      $this->view->page = 'home home-detail';
      $this->_detail($type_code, $code);
      return;
    }

    if (!empty($type_code)) {
      $this->_type($type_code);
    }
    $this->view->hd_full = empty($code) ? 'w-full' : null;
  }

  private function _type($type_code = null)
  {
    if (empty($type_code)) {
      return $this->response->redirect('/404');
    }

    $conditions = "H.status = :status:";
    $bind       = ['status' => 1];
    $menu       = null;

    if ($type_code == "xu-huong") {
      $hostingPremiumVip = HostingPremiumModel::find([
        "conditions"  => "premium_type = :vip:",
        "bind"        =>  [
          "vip"      =>  'vip',
        ],
      ]);

      $hostingVipIds = [];
      if (!empty($hostingPremiumVip) && count($hostingPremiumVip) > 0) {
        foreach ($hostingPremiumVip as $key => $value) {
          $hostingVipIds[] = $value->hosting_id;
        }
      }

      $conditions .= " AND H.id IN ({hostingVipIds:array})";
      $bind['hostingVipIds'] = $hostingVipIds;
      $menu = "xu-huong";
      $this->view->type = (object) [
        'code'      => 'xu-huong',
        'title'     => 'Chỗ ở xu hướng'
      ];
      $headTitle = "Danh sách chỗ ở xu hướng.";
      $headDesc  = 'Ohdidi tổng hợp danh sách các chỗ ở bao gồm homestay, villa, resort xu hướng, chi tiết với giá phòng công khai trên toàn quốc giúp bạn có một chuyến du lịch đáng nhớ.';
      $headUrl   = $this->url->get('/xu-huong');
    } elseif ($type_code == 'tat-ca') {
      $menu = "tat-ca";
      $this->view->type = (object) [
        'code'      => 'tat-ca',
        'title'     => 'Tất cả'
      ];

      $headTitle = "Tất cả cơ sở lưu trú trên toàn quốc.";
      $headDesc  = "Ohdidi là trang tổng hợp tất cả các homestay, villa, resort một cách đầy đủ, rõ ràng, công khai giá phòng trên toàn quốc giúp bạn có một chuyến du lịch đáng nhớ.";
      $headUrl   = $this->url->get('/tat-ca');
    } else {
      $type = HostingTypeModel::findFirst([
        'conditions'  => 'code = :code: AND status = :status:',
        'bind'        => ['code' => $type_code, 'status' => 1],
      ]);

      if (!$type) {
        return $this->response->redirect('/404');
      }

      $headTitle = !empty($type->seo_title) ? $type->seo_title : $type->title;
      $headDesc  = !empty($type->seo_description) ? $type->seo_description : $type->description;
      $headUrl   = $this->url->get('/' . $type_code);

      $conditions        .= " AND H.type_id = :type_id:";
      $bind['type_id']    = $type->id;
      $this->view->type   = $type;
      $menu               = $type->code;
    }

    $list_item = HostingModel::init()->GetHostingItem([
      'where'       => $conditions,
      'bind'        => $bind,
    ]);

    $pager = new Pager(
      new PaginatorModel(
        [
          'data'    => $list_item,
          'limit'   => 20,
          'page'    => $this->request->getQuery('page', 'int', 1),
        ]
      ),
      [
        'layoutClass' => '\Phalcon\Paginator\Pager\Layout\Bootstrap',
        'rangeLength' => 5,
        'urlMask' => "?page={%page_number}",
      ]
    );

    $this->view->head = [
      'title'       => $headTitle,
      'description' => $headDesc,
      'url'         => $headUrl
    ];

    $this->view->count_list_hosting   = count($list_item);
    $this->view->pager                = $pager;
    $this->view->menu                 = $menu;
    $this->view->pick('hosting/index');
  }

  private function _detail($type_code = null, $code = null)
  {

    if (empty($type_code) || empty($code)) {
      return $this->response->redirect('/404');
    }

    $type = HostingTypeModel::findFirstByCode($type_code);
    if (empty($type)) {
      return $this->response->redirect('/404');
    }

    $hosting = HostingModel::findFirst(
      [
        'conditions' => 'code = :code: and type_id = :type_id: and status = :status:',
        'bind'       => ['code' => $code, 'type_id' => $type->id, 'status' => 1],
      ]
    );

    if (empty($hosting)) {
      return $this->response->redirect('/404');
    }

    $hosting->hits = $hosting->hits + 1;
    $hosting->save();

    $hosting_img          = json_decode($hosting->imgs, true);
    $list_hosting_imgs    = array_column($hosting_img, 'src');

    $hosting_rules        = HostingRulesModel::findFirstByHostingId($hosting->id);
    $list_hosting_props   = HostingPropertiesHasModel::findByHostingId($hosting->id);

    $list_location_distance = HostingLocationDistanceModel::find(
      [
        "conditions" => "hosting_id = :hosting_id:",
        "bind"       => ["hosting_id" => $hosting->id],
        "order"      => "distance ASC",
      ]
    );

    $list_props_group     = HostingPropertiesGroupModel::findByStatus(1);
    $list_location_group  = LocationAreaGroupModel::findByStatus(1);

    $list_hosting_favorite = HostingModel::find(
      [
        'conditions' => 'code = :code: and district_code = :district_code: and status = :status: and id != :id:',
        'bind'       => ['code' => $code, 'district_code' => $hosting->district_code, 'status' => 1, 'id' => $hosting->id],
        "limit"      => 8,
      ]
    );

    $title        = $hosting->seo_title ? $hosting->seo_title : ($hosting->title ? $hosting->title . ' - ' : '') . ($hosting->hosting_type->title ? $hosting->hosting_type->title . ' tại ' : '') . ($hosting->province->name ? $hosting->province->name : '') . (!empty($hosting->url_review) ? ' - Có video review' : '');
    $description  = $hosting->seo_description ? $hosting->seo_description : substr($hosting->content, 0, 300);
    $image        = $this->url->get($hosting->image);
    $url          = $this->url->get('/' . $hosting->hosting_type->code . '/' . $hosting->code);


    $allowBookingOnline = $this->Ohi->checkAllowBookingOnline($hosting->id);

    // Load đánh giá của hosting
    $reviewModel = new \Modules\App\Models\BookingReviewModel();
    $hosting_reviews = $reviewModel->getLatestReviews($hosting->id, 10);

    $this->view->allowBookingOnline         = $allowBookingOnline;
    $this->view->head                       = compact('title', 'description', 'image', 'url');
    $this->view->list_hosting_imgs          = $list_hosting_imgs;
    $this->view->hosting_rules              = $hosting_rules;
    $this->view->list_hosting_favorite      = $list_hosting_favorite;
    $this->view->list_hosting_props         = $list_hosting_props;
    $this->view->list_props_group           = $list_props_group;
    $this->view->list_location_distance     = $list_location_distance;
    $this->view->list_location_group        = $list_location_group;
    $this->view->hosting                    = $hosting;
    $this->view->hosting_reviews            = $hosting_reviews;
    $this->view->pick("hosting/detail/main");
  }

  public function searchAction()
  {
    $this->view->hd_full  = 'w-full';
    $this->view->page     = 'home';
    $params = $this->request->getQuery();
    if (empty($params)) {
      $this->response->redirect('/tat-ca');
    }
    $conditions   = 'H.status = :status:';
    $bind         = ['status' => 1];

    if (!empty($params['keyWord'])) {
      $keyWord = mb_strtolower($params['keyWord'], 'UTF-8');
      $keywords = explode(' ', $keyWord);
      $keywordConditions = [];

      foreach ($keywords as $index => $word) {
        $keywordConditions[] = '(H.title LIKE :key_word_' . $index . ': OR H.code LIKE :key_word_' . $index . ':)';
        $bind['key_word_' . $index] = '%' . $word . '%';
      }

      if (!empty($keywordConditions)) {
        $conditions .= ' AND ' . implode(' AND ', $keywordConditions);
      }
    }

    if (!empty($params['province'])) {
      $conditions .= ' AND H.province_code = :province_code:';
      $bind['province_code'] = $params['province'];
    }

    if (!empty($params['district'])) {
      $conditions .= ' AND H.district_code = :district_code:';
      $bind['district_code'] = $params['district'];
    }

    if (!empty($params['minPrice'])) {
      $conditions .= ' AND (H.price >= :min_price:)';
      $bind['min_price'] = $params['minPrice'];
    }

    if (!empty($params['maxPrice'])) {
      $conditions .= ' AND (H.price <= :max_price:)';
      $bind['max_price'] = $params['maxPrice'];
    }

    if (!empty($params['type'])) {
      $conditions .= ' AND H.type_id IN ({arrTypes:array})';
      $bind['arrTypes'] = $params['type'];
    }

    if (!empty($params['qntGuest'])) {
      $conditions .= ' AND (H.qnt_guest >= :qnt_guest: OR H.qnt_guest = 0)';
      $bind['qnt_guest'] = $params['qntGuest'];
    }

    if (!empty($params['roomType'])) {
      $conditions .= ' AND HR.room_type_id IN ({room_type:array})';
      $bind['room_type'] = $params['roomType'];
    }

    if (!empty($params['props'])) {
      if (is_array($params['props']) && count($params['props']) > 0) {
        $arrHostingFinal = [];

        foreach ($params['props'] as $key => $val) {
          if ($key == 0) {
            $listHostingPrimary = HostingPropertiesHasModel::findByProperties_id($val);

            foreach ($listHostingPrimary as $val0) {
              $arrHostingFinal[] = $val0->hosting_id;
            }
          } else {
            if (empty($arrHostingFinal)) {
              continue;
            }
            $listHostingSecondary = HostingPropertiesHasModel::find([
              'conditions' => 'hosting_id IN ({arrHostingFinal:array}) AND properties_id = :properties_id:',
              'bind'       => [
                'arrHostingFinal' => $arrHostingFinal,
                'properties_id'   => $val,
              ],
            ]);

            $arrHostingFinal = [];
            foreach ($listHostingSecondary as $key2 => $val2) {
              $arrHostingFinal[] = $val2->hosting_id;
            }
          }
        }
        if (!empty($arrHostingFinal)) {
          $conditions .= ' AND H.id IN ({arrHostingFinal:array})';
          $bind['arrHostingFinal'] = $arrHostingFinal;
        }
      } else {
        $conditions .= ' AND PH.properties_id IN ({arrProps:array})';
        $bind['arrProps'] = $params['props'];
      }
    }

    if (isset($params['review']) && $params['review'] == 1) {
      $conditions .= ' AND H.url_review != ""';
    }

    if (!empty($params['rentalType'])) {
      $conditions .= ' AND H.rental_type = :rental_type:';
      $bind['rental_type'] = $params['rentalType'];
    }

    if (!empty($params['sort'])) {
      $order = 'H.price ' . $params['sort'];
    }

    if (isset($params['province']) && !empty($params['province'])) {
      $this->view->list_district_search = LocationDistrictModel::find([
        'conditions' => 'province_code = :province_code:',
        'bind'       => ['province_code' => $params['province']],
        'order'      => 'name ASC'
      ]);

      $province = LocationProvinceModel::findFirstByCode($params['province']);
      $this->view->provinceName  = $province->name;
      $this->view->provinceType  = $province->type;

      $this->view->head = [
        'title'       => 'Chỗ ở tại ' . $province->type . ' ' . $province->name,
        'description' => 'Chỗ ở xu hướng dành cho kỳ nghỉ của bạn ở ' . $province->type . ' ' . $province->name . ' tại thời điểm này.',
        'url'         => $this->url->get('/tim-kiem?province=' . $params['province']),
      ];
    }

    if (isset($params['district']) && !empty($params['district'])) {
      $district = LocationDistrictModel::findFirstByCode($params['district']);

      $this->view->head = [
        'title'       => 'Chỗ ở tại ' . $district->type . ' ' . $district->name,
        'description' => 'Chỗ ở xu hướng dành cho kỳ nghỉ của bạn ở ' . $district->type . ' ' . $district->name . ' tại thời điểm này.',
        'url'         => $this->url->get('/tim-kiem?province=' . $params['province'] . '&district=' . $params['district']),
      ];

      $this->view->districtName  = $district->name;
      $this->view->districtType  = $district->type;
    }

    $this->view->params = $params;
    $urlParams = http_build_query($params);
    if (!empty($params['sort'])) {
      $list_hosting = HostingModel::init()->GetHostingItem([
        'where'  => $conditions,
        'bind'   => $bind,
        'order'  => $order
      ]);
    } else {
      $list_hosting = HostingModel::init()->GetHostingItem([
        'where'  => $conditions,
        'bind'   => $bind,
      ]);
    }

    if (!empty($params['distance'])) {
      if (!empty($params['district'])) {
        $location = LocationDistrictModel::findFirstByCode($params['district']);
      } else if (!empty($params['province'])) {
        $location = LocationProvinceModel::findFirstByCode($params['province']);
      }
      $attrDistanceId = [];
      if ($location) {
        foreach ($list_hosting as $val) {
          $hostingDistance = Maps::distance_between_2point($location->longitude, $val->longitude, $location->latitude, $val->latitude);
          $distance = (float)$hostingDistance['kilometers'];
          switch ($params['distance']) {
            case '1':
              if ($distance < 1) $attrDistanceId[] = $val->id;
              break;
            case '2':
              if ($distance >= 1 && $distance <= 2) $attrDistanceId[] = $val->id;
              break;
            case '3':
              if ($distance > 2 && $distance <= 5) $attrDistanceId[] = $val->id;
              break;
            case '4':
              if ($distance > 5 && $distance <= 10) $attrDistanceId[] = $val->id;
              break;
            case '5':
              if ($distance > 10) $attrDistanceId[] = $val->id;
              break;
            default:
              break;
          }
        }
      }
    }

    if (!empty($attrDistanceId) && count($attrDistanceId) > 0) {
      $list_hosting = HostingModel::init()->GetHostingItem([
        'where'  => 'H.id IN (' . implode(',', $attrDistanceId) . ')',
        'bind'   => $bind,
        'order'  => $order,
      ]);
    }

    $pager = new Pager(
      new PaginatorModel(
        [
          'data' => $list_hosting,
          'limit' => 20,
          'page' => $this->request->getQuery('page', 'int', 1),
        ]
      ),
      [
        'layoutClass' => '\Phalcon\Paginator\Pager\Layout\Bootstrap',
        'rangeLength' => 5,
        'urlMask' => "?$urlParams&page={%page_number}",
      ]
    );
    $this->view->count_list_hosting = count($list_hosting);
    $this->view->pager = $pager;
    $this->view->pick("hosting/search");
  }

  public function hostingViewImgsAction()
  {
    if ($this->request->isAjax()) {
      $hostingId = $this->request->getPost('hostingId', null, 0);
      if ($hostingId > 0) {
        $hosting        = HostingModel::findFirstById($hostingId);
        $hostingImg     = json_decode($hosting->imgs, true);
        $listImgs       = array_column($hostingImg, 'src');
      }
      die(json_encode($listImgs) ?? []);
      $this->view->setRenderLevel(\Phalcon\Mvc\View::LEVEL_ACTION_VIEW);
    }
  }

  public function getDetailHostingRoomAction()
  {
    $this->view->disable();
    $resp = [
      'status'    => false,
      'msg'       => '',
      'result'    => ''
    ];

    if (!$this->request->isAjax() || !$this->request->isPost()) {
      return;
    }
    try {
      $roomId = $this->request->getPost('roomId', null, 0);
      if (!$roomId) {
        throw new \Exception('Giá trị không hợp lệ');
      }

      $room = HostingRoomModel::findFirstById($roomId);

      if (empty($room)) {
        throw new \Exception('Không tìm thấy phòng');
      }

      $roomBed          = HostingRoomBedModel::findByRoom_id($room->id);
      $roomProperties   = HostingRoomPropertiesModel::findByRoom_id($room->id);
      $resp['status']   = true;
      $resp['msg']      = 'Thành công';
      $resp['result']   = $this->view->getRender(
        'hosting/room',
        'itemDetail',
        [
          'room'            => $room,
          'roomBed'         => $roomBed,
          'roomProperties'  => $roomProperties,
        ],
        function ($view) {
          $view->setRenderLevel(\Phalcon\Mvc\View::LEVEL_ACTION_VIEW);
        }
      );
    } catch (\Exception $e) {
      $resp['msg'] = $e->getMessage();
    }
    die(json_encode($resp));
  }

  public function getDetailHostingVillaAction()
  {
    $this->view->disable();
    $resp = [
      'status'    => false,
      'msg'       => '',
      'result'    => ''
    ];

    if (!$this->request->isAjax() || !$this->request->isPost()) {
      return;
    }

    try {
      $villaId = $this->request->getPost('villaId', null, 0);
      if (!$villaId) {
        throw new \Exception('Giá trị không hợp lệ');
      }

      $villa = HostingVillaModel::findFirstById($villaId);
      if (empty($villa)) {
        throw new \Exception('Không tìm thấy villa');
      }

      $villaItem = HostingVillaItemModel::findByVilla_id($villa->id);
      $villaProps = HostingVillaPropertiesModel::findByVilla_id($villa->id);

      $resp['status'] = true;
      $resp['msg']    = 'Thành công';
      $resp['result'] = $this->view->getRender(
        'hosting/villa',
        'itemDetail',
        [
          'villa'           => $villa,
          'villaItem'       => $villaItem,
          'villaProps'      => $villaProps,
        ],
        function ($view) {
          $view->setRenderLevel(\Phalcon\Mvc\View::LEVEL_ACTION_VIEW);
        }
      );
    } catch (\Exception $e) {
      $resp['msg'] = $e->getMessage();
    }
    die(json_encode($resp));
  }

  public function getFavoriteAction()
  {
    $out = [
      'status'    => false,
      'msg'       => '',
      'result'    => ''
    ];
    if ($this->request->isAjax() && $this->request->isPost()) {
      $user = $this->user->item;
      $list_hosting_favorite = HostingSavedModel::find([
        'conditions' => 'user_id = :user_id:',
        'bind' => [
          'user_id' => $user['id'],
        ]
      ]);

      if (!empty($list_hosting_favorite)) {
        $out['status']  = true;
        $out['result']  = $this->view->getRender(
          'hosting',
          'hostingFavoriteList',
          [
            'list_hosting_favorite' => $list_hosting_favorite
          ],
          function ($view) {
            $view->setRenderLevel(\Phalcon\Mvc\View::LEVEL_ACTION_VIEW);
          }
        );
      }
    }

    die(json_encode($out));
  }

  /**
   * Load more reviews via AJAX
   */
  public function loadMoreReviewsAction()
  {
    $this->view->disable();
    $response = [
      'status' => false,
      'data' => '',
      'hasMore' => false,
      'message' => ''
    ];

    if (!$this->request->isAjax() || !$this->request->isPost()) {
      $response['message'] = 'Invalid request';
      die(json_encode($response));
    }

    try {
      $hostingId = $this->request->getPost('hosting_id', 'int', 0);
      $page = $this->request->getPost('page', 'int', 1);
      $limit = 10;
      $offset = ($page - 1) * $limit;

      if (!$hostingId) {
        throw new \Exception('Invalid hosting ID');
      }

      // Load reviews with pagination
      $reviewModel = new \Modules\App\Models\BookingReviewModel();
      $reviews = $reviewModel->getReviewsPaginated($hostingId, $limit, $offset);
      $totalReviews = $reviewModel->getTotalReviews($hostingId);

      if (!empty($reviews)) {
        // Render reviews HTML
        $html = '';
        foreach ($reviews as $review) {
          $html .= $this->view->getRender(
            'hosting/detail',
            'review-item',
            [
              'review' => $review,
              'url' => $this->url
            ]
          );
        }

        $response['status'] = true;
        $response['data'] = $html;
        $response['hasMore'] = ($offset + $limit) < $totalReviews;
      } else {
        $response['message'] = 'No more reviews found';
      }

    } catch (\Exception $e) {
      $response['message'] = $e->getMessage();
    }

    die(json_encode($response));
  }

  /**
   * Check if there are more reviews to load
   */
  public function checkMoreReviewsAction()
  {
    $this->view->disable();
    $response = [
      'status' => false,
      'hasMore' => false,
      'message' => ''
    ];

    if (!$this->request->isAjax() || !$this->request->isPost()) {
      $response['message'] = 'Invalid request';
      die(json_encode($response));
    }

    try {
      $hostingId = $this->request->getPost('hosting_id', 'int', 0);
      $currentCount = $this->request->getPost('current_count', 'int', 0);

      if (!$hostingId) {
        throw new \Exception('Invalid hosting ID');
      }

      $reviewModel = new \Modules\App\Models\BookingReviewModel();
      $totalReviews = $reviewModel->getTotalReviews($hostingId);

      $response['status'] = true;
      $response['hasMore'] = $currentCount < $totalReviews;

    } catch (\Exception $e) {
      $response['message'] = $e->getMessage();
    }

    die(json_encode($response));
  }

  /**
   * Get review images for gallery
   */
  public function getReviewImagesAction()
  {
    if ($this->request->isAjax()) {
      $reviewId = $this->request->getPost('reviewId', null, 0);
      if ($reviewId > 0) {
        $reviewModel = new \Modules\App\Models\BookingReviewModel();
        $review = $reviewModel->findFirstById($reviewId);
        if ($review && $review->imgs) {
          $reviewImgs = json_decode($review->imgs, true);
          if (is_array($reviewImgs)) {
            die(json_encode($reviewImgs));
          }
        }
      }
      die(json_encode([]));
      $this->view->setRenderLevel(\Phalcon\Mvc\View::LEVEL_ACTION_VIEW);
    }
  }


}

/* End of file HostingController.php */
/* Location: ./apps/frontend/controllers/HostingController.php */
