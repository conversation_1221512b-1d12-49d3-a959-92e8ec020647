<?php
	/**
	 * Registering a router
	 */
	$router = new \Phalcon\Mvc\Router(false);
	$router->removeExtraSlashes(true);
	$router->setDefaultModule("frontend");
	$router->setDefaultAction("index");
	$router->setDefaultController("index"); 
	
	/* Frontend */
		$router->add('/:controller', array(
			'module' 		=> 'frontend',
			'controller'=> 1,
			'action' 		=> 'index'
		));
		$router->add('/:controller/:action', array(
			'module' 		=> 'frontend',
			'controller'=> 1,
			'action' 		=> 2
		));
		$router->add('/:controller/:action/:params', array(
			'module' 		=> 'frontend',
			'controller'=> 1,
			'action' 		=> 2,
			'params'		=> 3
		));

		//hosting
		$router->add('/:params', array(
			'module' 		=> 'frontend',
			'controller'=> 'hosting',
			'action' 		=> 'index',
			'params'		=> 1
		));

		//index
		$router->add('/', array(
			'module' 		=> 'frontend',
			'controller'=> 'index',
			'action' 		=> 'index',
			'segment' 	=> 'home',
		));

		$router->add('/tai-app-ngay', array(
			'module' 		=> 'frontend',
			'controller'=> 'index',
			'action' 		=> 'downloadApp',
		));

		$router->add('/danh-cho-host', array(
			'module' 		=> 'frontend',
			'controller'=> 'host',
			'action' 		=> 'forHost',
		));

		$router->add('/gioi-thieu', array(
			'module' 		=> 'frontend',
			'controller'=> 'index',
			'action' 		=> 'about',
		));
		
		$router->add('/recharge/paygetReturn', array(
			'module' 		=> 'frontend',
			'controller'=> 'recharge',
			'action' 		=> 'paygetReturn',
		));

		$router->add('/host/:params', array(
			'module' 		=> 'frontend',
			'controller'=> 'host',
			'action' 		=> 'index',
			'params'		=> 1,
		));

		/* Tài khoản */
		$router->add('/account/:params', array(
			'module' 		=> 'frontend',
			'controller'=> 'account',
			'action' 		=> 'index',
			'params'		=> 1,
		));
		
		$router->add('/booking/:params', array(
			'module' 		=> 'frontend',
			'controller'=> 'booking',
			'action' 		=> 'index',
			'params'		=> 1,
		));

		$router->add('/booking/booking-print', array(
			'module' 		=> 'frontend',
			'controller'=> 'booking',
			'action' 		=> 'print',
		));
		$router->add('/401', array(
			'module' 		=> 'frontend',
			'controller'=> 'error',
			'action' 		=> 'show401'
		));

		$router->add('/404', array(
			'module' 		=> 'frontend',
			'controller'=> 'error',
			'action' 		=> 'show404'
		));
		
		$router->add('/maintain', array(
			'module' 		=> 'frontend',
			'controller'=> 'error',
			'action' 		=> 'maintain'
		));
		$router->add('/review/:params', array(
			'module' 		=> 'frontend',
			'controller'=> 'review',
			'action' 		=> 'index',
			'params' 		=> 1
		));
		// Province
		$router->add('/province', array(
			'module' 		=> 'frontend',
			'controller'=> 'index',
			'action' 		=> 'province'
		));	

		$router->add('/district', array(
			'module' 		=> 'frontend',
			'controller'=> 'index',
			'action' 		=> 'district'
		));	

		// News
		$router->add('/news/filterItem', array(
			'module' 		=> 'frontend',
			'controller'=> 'news',
			'action' 		=> 'filterItem'
		));

		$router->add('/blog/:params', array(
			'module' 		=> 'frontend',
			'controller'=> 'news',
			'action' 		=> 'index',
			'params' 		=> 1
		));

		$router->add('/thong-tin/:params', array(
			'module' 		=> 'frontend',
			'controller'=> 'news',
			'action' 		=> 'info',
			'params' 		=> 1
		));

		$router->add('/hosting/hostingViewImgs', array(
			'module' 		=> 'frontend',
			'controller'=> 'hosting',
			'action' 		=> 'hostingViewImgs',
		));

		$router->add('/hosting/getDetailHostingRoom', array(
			'module' 		=> 'frontend',
			'controller'=> 'hosting',
			'action' 		=> 'getDetailHostingRoom',
		));

		$router->add('/hosting/getDetailHostingVilla', array(
			'module' 		=> 'frontend',
			'controller'=> 'hosting',
			'action' 		=> 'getDetailHostingVilla',
		));

		$router->add('/hosting/getFavorite', array(
			'module' 		=> 'frontend',
			'controller'=> 'hosting',
			'action' 		=> 'getFavorite',
		));

		$router->add('/hosting/loadMoreReviews', array(
			'module' 		=> 'frontend',
			'controller'=> 'hosting',
			'action' 		=> 'loadMoreReviews',
		));

		$router->add('/hosting/checkMoreReviews', array(
			'module' 		=> 'frontend',
			'controller'=> 'hosting',
			'action' 		=> 'checkMoreReviews',
		));

		$router->add('/hosting/getReviewImages', array(
			'module' 		=> 'frontend',
			'controller'=> 'hosting',
			'action' 		=> 'getReviewImages',
		));

		$router->add('/hosting/debugReview', array(
			'module' 		=> 'frontend',
			'controller'=> 'hosting',
			'action' 		=> 'debugReview',
		));

		$router->add('/filterProvince', array(
			'module' 		=> 'frontend',
			'controller'=> 'index',
			'action' 		=> 'filterProvinceAndDistrict',
		));

		$router->add('/tim-kiem', array(
			'module' 		=> 'frontend',
			'controller'=> 'hosting',
			'action' 		=> 'search',
		));
		
		// Contact
		$router->add('/huong-dan/:params', array(
			'module' 		=> 'frontend',
			'controller'=> 'contact',
			'action' 		=> 'index',
			'params' 		=> 1,
		));

		$router->add('/lien-he', array(
			'module' 		=> 'frontend',
			'controller'=> 'contact',
			'action' 		=> 'main'
		));
		$router->add('/gui-lien-he', array(
			'module' 		=> 'frontend',
			'controller'=> 'contact',
			'action' 		=> 'send'
		));

		$router->add('/gui-lien-he', array(
			'module' 		=> 'frontend',
			'controller'=> 'contact',
			'action' 		=> 'send'
		));

		// Auth
		$router->add('/auth/dang-nhap', array(
			'module' 		=> 'frontend',
			'controller'=> 'auth',
			'action' 		=> 'login'
		));

		$router->add('/auth/google-login', array(
			'module' 		=> 'frontend',
			'controller'=> 'auth',
			'action' 		=> 'loginGoogle'
		));

		$router->add('/auth/google-login-callback', array(
			'module' 		=> 'frontend',
			'controller'=> 'auth',
			'action' 		=> 'loginGoogleCallback'
		));

		$router->add('/auth/facebook-login', array(
			'module' 		=> 'frontend',
			'controller'=> 'auth',
			'action' 		=> 'loginFacebook'
		));

		$router->add('/auth/facebook-login-callback', array(
			'module' 		=> 'frontend',
			'controller'=> 'auth',
			'action' 		=> 'loginFacebookCallback'
		));

		$router->add('/auth/dang-xuat', array(
			'module' 		=> 'frontend',
			'controller'=> 'auth',
			'action' 		=> 'logout'
		));

		$router->add('/auth/dang-ky', array(
			'module' 		=> 'frontend',
			'controller'=> 'auth',
			'action' 		=> 'register'
		));

		$router->add('/auth/gui-xac-thuc', array(
			'module' 		=> 'frontend',
			'controller'=> 'auth',
			'action' 		=> 'sendOTPVerify'
		));

		$router->add('/auth/xac-nhan-otp', array(
			'module' 		=> 'frontend',
			'controller'=> 'auth',
			'action' 		=> 'verifyOTP'
		));

		$router->add('/auth/gui-xac-thuc-lai', array(
			'module' 		=> 'frontend',
			'controller'=> 'auth',
			'action' 		=> 'resendOTP'
		));
		

		$router->add('/auth/xac-thuc', array(
			'module' 		=> 'frontend',
			'controller'=> 'auth',
			'action' 		=> 'verify'
		));

		//ZNS 

		$router->add('/zns', array(
			'module' 		=> 'frontend',
			'controller'=> 'index',
			'action' 		=> 'zns'
		));

		// Payment
		$router->add('/nap-tien', array(
			'module' 			=> 'frontend',
			'controller' 	=> 'payment',
			'action' 			=> 'index'
		));

		$router->add('/nap-tien/recharge', array(
			'module' 			=> 'frontend',
			'controller' 	=> 'payment',
			'action' 			=> 'recharge'
		));

		$router->add('/nap-tien/vnpay-result', array(
			'module' 			=> 'frontend',
			'controller' 	=> 'payment',
			'action' 			=> 'vnpayResult'
		));

		$router->add('/vnpay-ipn', array(
			'module' 			=> 'frontend',
			'controller' 	=> 'payment',
			'action' 			=> 'vnpayIpn'
		));

		$router->add('/casso-ipn', array(
			'module' 			=> 'frontend',
			'controller' 	=> 'payment',
			'action' 			=> 'cassoIpn'
		));

		$router->add('/payment-debug', array(
			'module' 			=> 'frontend',
			'controller' 	=> 'payment',
			'action' 			=> 'cassoDebug'
		));

		//APP Content
		$router->add('/app/:params', array(
			'module' 		=> 'frontend',
			'controller'=> 'index',
			'action' 		=> 'appContent',
			'params'		=> 1
		));

	/* Backend */
		$router->add("/admin", array(
			'module' 		=> 'backend',
			'controller'=> 'index',
			'action' 		=> 'index',
		));
		
		$router->add('/admin/:controller', array(
			'module' 		=> 'backend',
			'controller'=> 1,
			'action' 		=> 'index',
		));
		$router->add('/admin/:controller/:action', array(
			'module' 		=> 'backend',
			'controller'=> 1,
			'action' 		=> 2,
		));
		$router->add('/admin/:controller/:action/:params', array(
			'module' 		=> 'backend',
			'controller'=> 1,
			'action' 		=> 2,
			'params'		=> 3,
		));

		require_once 'api.php';

	return $router;