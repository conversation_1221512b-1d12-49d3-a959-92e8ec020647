<?php

namespace Modules\Library\Oh;

use DateTime;
use Modules\App\Models\BookingGuestLogModel;
use Modules\App\Models\HostingModel;
use Modules\App\Models\HostingPremiumModel;
use Modules\App\Models\HostingRoomModel;
use Modules\App\Models\HostingPropertiesHasModel;
use Modules\App\Models\HostingRoomPropertiesModel;
use Modules\App\Models\BookingModel;
use Modules\App\Models\BookingTransactionModel;
use Modules\App\Models\BookingVillaModel;
use Modules\App\Models\ErpBusinessKpiModel;
use Modules\App\Models\ErpBusinessModel;
use Modules\App\Models\ErpMemberModel;
use Modules\App\Models\EventVoucherModel;
use Modules\App\Models\HostingPropertiesGroupModel;
use Modules\App\Models\HostingRoomBedModel;
use Modules\App\Models\HostingRoomTypeModel;
use Modules\App\Models\HostingTypeModel;
use Modules\App\Models\HostingVillaItemBedModel;
use Modules\App\Models\HostingVillaItemModel;
use Modules\App\Models\HostingVillaItemRoomModel;
use Modules\App\Models\HostingVillaModel;
use Modules\App\Models\HostingVillaPropertiesModel;
use Modules\App\Models\HostingWholeRoomModel;
use Modules\App\Models\LocationDistrictModel;
use Modules\App\Models\LocationProvinceModel;
use Modules\App\Models\SystemConfigModel;
use Modules\App\Models\PaymentTransactionModel;
use Modules\App\Models\UserVerifyModel;
use Modules\App\Models\UserVoucherModel;
use Phalcon\Di\Injectable;

class Ohi extends Injectable
{
	public $telegram_id 					= '-907053546'; // group ohdidi
	public $telegram_service_id 	= '-4007484882'; // group ohdidi - dịch vụ
	public $telegram_bookers_id 	= '-4253072442'; // group ohdidi - bookers
	public $telegram_bot_id 			= '-4171874451'; // group dịch vụ - BOT
	public $telegram_team_ohdidi	= '-4639649868'; // group team ohdidi	
	// public $telegram_bot_id 			= '-4696156190'; // group dịch vụ - BOT

	// Rental type
	public $rental_type_villa 			= 'villa';
	public $rental_type_whole 			= 'whole';
	public $rental_type_room 				= 'room';
	public $rental_type_hour 				= 'hour';
	public $rental_type_villa_room 	= 'villa_room';

	// Review
	public $status_review_waiting 	= 'waiting';
	public $status_review_recorded 	= 'recorded';
	public $status_review_completed = 'completed';

	// Booking
	public $status_booking_init 			= 'init';
	public $status_booking_payment 		= 'payment';
	public $status_booking_pending 		= 'pending';
	public $status_booking_confirmed 	= 'confirmed';
	public $status_booking_cancelled 	= 'cancelled';
	public $status_booking_completed 	= 'completed';

	// channel
	public $booking_channel_website 	= 'website';
	public $booking_channel_facebook 	= 'facebook';
	public $booking_channel_instagram = 'instagram';
	public $booking_channel_tiktok 		= 'tiktok';
	public $booking_channel_zalo 			= 'zalo';

	// Payment
	public $status_payment_success 	= '00';
	public $status_payment_init 		= 'init';
	public $status_payment_fail 		= 'fail';


	// QR 
	public $qr_template 			= '9YkMCAB';
	public $qr_bank_name 			= 'Ngân hàng TMCP Công thương Việt Nam - Viettinbank';
	public $qr_bank_code 			= 'ICB';
	public $qr_bank_number 		= '************';
	public $qr_account_name 	= 'QUY QUOC';

	// Regex
	public $phoneRegex = '/^(0)(3[2-9]|5[6|8|9]|7[0|6-9]|8[0-9]|9[0-9])([0-9]{7})$/';
	public $emailRegex = '/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/';

	// Mobile app
	public $url_googleplay 	= 'https://play.google.com/store/apps/details?id=com.ohdidi';
	public $url_appstore 		= 'https://apps.apple.com/vn/app/ohdidi/id6743406750';

	public function convertDate($dateStr)
	{
		$date = \DateTime::createFromFormat('Y-m-d', $dateStr);
		if (!$date) {
			return 'Ngày không hợp lệ';
		}

		$timestamp = $date->getTimestamp();
		$dateFormatted = date('N, j \t\há\n\g n, Y', $timestamp);

		$dayOfWeek = [
			1 => 'T2',
			2 => 'T3',
			3 => 'T4',
			4 => 'T5',
			5 => 'T6',
			6 => 'T7',
			7 => 'CN'
		];

		sscanf($dateFormatted, '%d, %d tháng %d, %d', $weekday, $day, $month, $year);

		$result = $dayOfWeek[$weekday] . ", " . $day . " tháng " . $month . ", " . $year;

		return $result;
	}

	function getDatesBetween($startDate, $endDate)
	{
		$dates = array();
		$currentDate = strtotime($startDate);
		$endDate = strtotime($endDate);

		while ($currentDate <= $endDate) {
			$dates[] = date('Y-m-d', $currentDate);
			$currentDate = strtotime('+1 day', $currentDate);
		}

		return $dates;
	}

	public function formatDate($date = null)
	{
		$result = date("d/m/Y");

		if (!empty($date)) {
			if (date("Y", strtotime($date)) == date("Y"))
				return date("d/m", strtotime($date));
			else
				return date("d/m", strtotime($date)) . "/" . substr(date("Y", strtotime($date)), 2);
		}

		return $result;
	}

	public function convertDateToTimeAgo($created_date)
	{
		$current_date = new DateTime();
		$created_date_obj = new DateTime($created_date);
		$interval = $current_date->diff($created_date_obj);
		if ($created_date_obj->format('Y') == $current_date->format('Y')) {
			if ($created_date_obj->format('Y-m') == $current_date->format('Y-m')) {
				if ($interval->days == 0 && $interval->h == 0 && $interval->i < 1) {
					return "Vừa xong";
				}

				if ($interval->days == 0) {
					if ($interval->h == 0) {
						return "$interval->i phút trước";
					} else {
						return "$interval->h giờ trước";
					}
				} elseif ($interval->days < 7) {
					return "$interval->days ngày trước";
				} else {
					$weeks_diff = floor($interval->days / 7);
					return "$weeks_diff tuần trước";
				}
			} else {
				return $created_date_obj->format('d \T\há\n\g m');
			}
		} else {
			return $created_date_obj->format('d \T\h\á\n\g m, Y');
		}
	}

	public function formatString($string)
	{
		$returned_string = preg_replace('/[\s.,;]+/', '', $string);

		return $returned_string;
	}

	public function randomNumber($min = null, $max = null)
	{
		if (empty($min) || empty($max)) {
			return "";
		}
		return rand($min, $max);
	}

	public function baseUrl()
	{
		return ROOT_URL;
	}

	public function baseAdminUrl($path = "")
	{
		if ($path && !empty($path)) {
			$path = '/' . preg_replace('@^/+@', '', $path);
		}
		return ROOT_URL . "/admin" . $path;
	}

	public function FrotendHomePath($dir = "")
	{
		return ROOT_URL . "/frontend/home/<USER>";
	}

	public function homeImagePath($file = "")
	{
		return ROOT_URL . "/frontend/home/<USER>/{$file}";
	}

	public function LibPath($lib_name, $type = 'css')
	{
		$path = "/library/";
		switch ($lib_name) {
			case "slick":
				$path .= "slick/slick/";
				if ($type == 'css') {
					$path .= "slick.css";
				} elseif ($type == 'js') {
					$path .= "slick.min.js";
				}
				break;
		}
		return $path;
	}

	public function link($href, $nav, $params = null)
	{
		$url = '#';
		switch ($nav) {
			case "gioi-thieu":
				if ($href && !empty($href)) {
					$url = $href;
				} else {
					$url = ROOT_URL . "/gioi-thieu";
					if ($params && !empty($params)) {
						$url .= "/" . $params;
					}
				}
				break;
			default:
				if ($href && !empty($href)) {
					$url = $href;
				} else {
					$url = ROOT_URL . "/{$nav}";
					if ($params && !empty($params)) {
						$url .= "/" . $params;
					}
				}
				break;
		}
		return $url;
	}

	public function getAvatar($user = null, $link = false, $class = '')
	{
		$fullname = $user->fullname;
		$fullname = explode(' ', $fullname);
		$first_char = mb_substr($fullname[0], 0, 1);
		$last_char = mb_substr($fullname[count($fullname) - 1], 0, 1);
		$alias_name = $first_char . $last_char;

		$color = !empty($user->color) ? $user->color : '#d81b60';
		$style = 'border-color:' . $color . ';';

		$result = '<div class="avatar img-circle ' . $class . '" style="' . $style . '" title="' . $user->username . '">';

		$result .= $link ? '<a class="avatar-wrap" href="' . ROOT_URL . '/admin/erp-member/member/' . $user->username . '" target="_blank">' : '<div class="avatar-wrap">';

		$result .= 	'<div class="avatar-text" style="background-color: ' . $color . '">' . $alias_name . '</div>';

		if (!empty($user->avatar)) {
			$result .= '<div class="avatar-img" style="background-image:url(\'' . ROOT_URL . $user->avatar . '\');"></div>';
		}

		$result .= $link ? '</a>' : '</div>';

		$result .= '</div>';

		return $result;
	}

	public function getMemberById($id)
	{
		$data = ErpMemberModel::findFirstById($id);
		return $data;
	}

	/** 
	 *  Lấy toàn bộ childCategoryID con từ 1 id cha arrCategory
	 *  @param $name_table [string]
	 *  @param $parents [integer]
	 *  @param $data [object]
	 */
	public function getChildCategoryId($name_table = null, $parents = null, &$data = null)
	{

		$table      = "\Modules\App\Models\\" . $name_table;
		$result     = $table::find(array(
			"conditions"    => "parents = :parents: AND status = 1",
			"bind"          => array('parents' => $parents),
			"columns"       => "id"
		));

		if (count($result) > 0) {
			foreach ($result as $key => $value) {
				$data[] = $value->id;
				$this->getChildCategoryId($name_table, $value->id, $data);
			}
		}
		$data[] = $parents;
	}

	public function activeMenuClass($segment, $currentSegment)
	{
		return strtolower($segment) == strtolower($currentSegment) ? 'active' : '';
	}

	public function sysConfig($code, $removeHtml = true, $field = 'data')
	{
		$data = SystemConfigModel::init()->findByCode($code, $field);
		if ($data && !empty($data)) {
			if ($removeHtml) {
				$data = strip_tags($data);
			}
			return $data;
		}
		return "";
	}

	public function checkProperties($propertiesId = null, $hostingId = null)
	{
		if (empty($propertiesId) || empty($hostingId) || $propertiesId <= 0 || $hostingId <= 0) {
			return '';
		}

		$properties = HostingPropertiesHasModel::init()->findFirst([
			'conditions' => 'properties_id = :propertiesId: AND hosting_id = :hosting_id:',
			'bind' => ['propertiesId' => $propertiesId, 'hosting_id' => $hostingId],
		]);

		return $properties ? 'checked' : '';
	}

	public function checkVillaProperties($propertiesId = null, $villa_id = null)
	{
		if (empty($propertiesId) || empty($villa_id) || $propertiesId <= 0 || $villa_id <= 0) {
			return '';
		}

		$properties = HostingVillaPropertiesModel::init()->findFirst([
			'conditions' => 'properties_id = :propertiesId: AND villa_id = :villa_id:',
			'bind' => ['propertiesId' => $propertiesId, 'villa_id' => $villa_id],
		]);

		return $properties ? 'checked' : '';
	}

	public function checkRoomProperties($propertiesId = null, $roomId = null)
	{
		if (empty($propertiesId) || empty($roomId) || $propertiesId <= 0 || $roomId <= 0) {
			return '';
		}

		$properties = HostingRoomPropertiesModel::init()->findFirst([
			'conditions' => 'properties_id = :propertiesId: AND room_id = :roomId:',
			'bind' => ['propertiesId' => $propertiesId, 'roomId' => $roomId],
		]);

		return $properties ? 'checked' : '';
	}

	public function checkRoomForWhole($roomId = null, $wholeId = null)
	{
		if (empty($roomId) || empty($wholeId) || $roomId <= 0 || $wholeId <= 0) {
			return '';
		}

		$room = HostingWholeRoomModel::findFirst([
			'conditions' 	=> 'room_id = :roomId: AND whole_id = :wholeId:',
			'bind' 				=> ['roomId' => $roomId, 'wholeId' => $wholeId],
		]);

		return !empty($room) ? 'checked' : '';
	}

	public function CountHostingByHost($host_id = null)
	{
		if (!empty($host_id) && $host_id > 0) {
			$list_host = HostingModel::findByHost_id($host_id);
		}

		return !empty($list_host) ? $list_host->count() : 0;
	}

	public function CountRoomPerType($hosting_id = null)
	{
		$rooms 			= 0;
		$room_types = 0;

		if (!empty($hosting_id) && $hosting_id > 0) {
			$list_room = HostingRoomModel::findByHosting_id($hosting_id);
			if ($list_room && $list_room->count() > 0) {

				$room_types = $list_room->count();

				foreach ($list_room as $key => $value) {
					$rooms = $rooms + $value->quantity;
				}
			}
		}
		return $rooms == 0 && $room_types == 0 ? "" : "{$rooms}/{$room_types}";
	}

	public function CountRoomByHost($hosting_id = null)
	{
		$rooms =	0;
		if (!empty($hosting_id) && $hosting_id > 0) {
			$list_room = HostingRoomModel::findByHosting_id($hosting_id);
			$rooms = count($list_room);
		}
		return $rooms;
	}

	public function getNameStatusReview($status = null)
	{
		$nameStatus = '';

		switch ($status) {
			case $this->status_review_waiting:
				$nameStatus = 'Chờ quay';
				break;
			case $this->status_review_recorded:
				$nameStatus = 'Đã quay';
				break;
			case $this->status_review_completed:
				$nameStatus = 'Hoàn thành';
				break;

			default:
				// code...
				break;
		}

		return $nameStatus;
	}

	public function getNameStatusPayment($status = null)
	{
		$nameStatus = '';

		switch ($status) {
			case $this->status_payment_init:
				$nameStatus = 'Khởi tạo';
				break;
			case $this->status_payment_success:
				$nameStatus = 'Thành công';
				break;
			case $this->status_payment_fail:
				$nameStatus = 'Thất bại';
				break;

			default:
				// code...
				break;
		}

		return $nameStatus;
	}

	public function getNameRentalTypeHosting($rental_type = null)
	{
		$name = '';

		switch ($rental_type) {
			case $this->rental_type_whole:
				$name = 'Nguyên căn';
				break;
			case $this->rental_type_room:
				$name = 'Phòng';
				break;
			case $this->rental_type_hour:
				$name = 'Giờ';
				break;
			case $this->rental_type_villa:
				$name = 'Villa';
				break;

			default:
				$name = 'Tất cả';
				break;
		}

		return $name;
	}

	public function getNameStatusBooking($status = null)
	{
		$nameStatus = '';

		switch ($status) {
			case $this->status_booking_init:
				$nameStatus = 'Khởi tạo';
				break;
			case $this->status_booking_payment:
				$nameStatus = 'Chờ thanh toán';
				break;
			case $this->status_booking_pending:
				$nameStatus = 'Chờ xác nhận';
				break;
			case $this->status_booking_confirmed:
				$nameStatus = 'Đã xác nhận';
				break;
			case $this->status_booking_cancelled:
				$nameStatus = 'Đã Hủy';
				break;
			case $this->status_booking_completed:
				$nameStatus = 'Hoàn thành';
				break;

			default:
				// code...
				break;
		}

		return $nameStatus;
	}

	public function getNameBookingChannel($channel = null)
	{
		$nameChannel = '';

		switch ($channel) {
			case $this->booking_channel_website:
				$nameChannel = 'Website';
				break;
			case $this->booking_channel_facebook:
				$nameChannel = 'Facebook';
				break;
			case $this->booking_channel_instagram:
				$nameChannel = 'Instagram';
				break;
			case $this->booking_channel_tiktok:
				$nameChannel = 'Tiktok';
				break;
			case $this->booking_channel_zalo:
				$nameChannel = 'Zalo';
				break;

			default:
				// code...
				break;
		}

		return $nameChannel;
	}

	public function BookingItemSumRecharge($booking_id = null)
	{

		$recharge = 0;

		if ($booking_id > 0) {
			$recharge = BookingTransactionModel::sum(array(
				"column" 			=> "recharge",
				"conditions"	=> "booking_id = :booking_id:",
				"bind"				=> ["booking_id" => $booking_id]
			));

			$recharge = $recharge && $recharge > 0 ? $recharge : 0;
		}
		return $recharge;
	}

	public function BookingItemSumBenefit($booking_id = null)
	{

		$recharge = 0;

		if ($booking_id > 0) {
			$recharge = BookingTransactionModel::sum(array(
				"column" 			=> "benefit",
				"conditions"	=> "booking_id = :booking_id:",
				"bind"				=> ["booking_id" => $booking_id]
			));

			$recharge = $recharge && $recharge > 0 ? $recharge : 0;
		}
		return $recharge;
	}

	public function GetPremiumStatus($hosting_id = null)
	{
		if (!empty($hosting_id) && $hosting_id > 0) {
			$premium = HostingPremiumModel::findFirstByHosting_id($hosting_id);
		}
		return $premium ?? '';
	}

	public function confirmation_code()
	{
		$key = '';
		$keys = range(0, 9);

		for ($i = 0; $i < 10; $i++) {
			$key .= $keys[array_rand($keys)];
		}
		if ($this->check_uni($key) == true) {
			return $key;
		} else {
			$this->confirmation_code();
		}
	}

	public function check_uni($random)
	{
		$check = BookingModel::findFirstByConfirmation_code($random);
		if (!empty($check)) {
			return false;
		} else {
			return true;
		}
	}

	public function pin_code()
	{
		$key = '';
		$keys = range(0, 9);

		for ($i = 0; $i < 4; $i++) {
			$key .= $keys[array_rand($keys)];
		}
		return $key;
	}

	public function BusinessSumBenefitByServicer($member_id = null, $kpi_id = null, $month = null, $year = null)
	{
		if (!empty($kpi_id)) {

			$kpi_team = ErpBusinessKpiModel::findFirst(array(
				"conditions" 	=> "id = :id:",
				"bind"				=> ["id" => $kpi_id]
			));
		}

		$month	= !empty($kpi_team) ? $kpi_team->kpi_month : $month;
		$year		= !empty($kpi_team) ? $kpi_team->kpi_year : $year;

		$sum = ErpBusinessModel::init()->SumBookingBenefit(array(
			"department_id" => !empty($kpi_team) ? $kpi_team->department_id : null,
			"member_id" 		=> !empty($member_id) ? $member_id : null,
			"month"  				=> $month,
			"year"    			=> $year,
		));

		return $sum;
	}

	public function BusinessBookingTotalBenefit($booking_id = null)
	{
		$benefit = 0;

		if ($booking_id > 0) {
			$benefit = BookingTransactionModel::sum(array(
				"column" 			=> "benefit",
				"conditions"	=> "booking_id = :booking_id:",
				"bind"				=> ["booking_id" => $booking_id]
			));

			$benefit = $benefit && $benefit > 0 ? $benefit : 0;
		}
		return $benefit;
	}

	public function GetQntWholeRoom($whole_id, $room_id)
	{
		$qnt = 0;
		$room = HostingWholeRoomModel::findFirst(array(
			"conditions" 	=> "whole_id = :whole_id: AND room_id = :room_id:",
			"bind"				=> ["whole_id" => $whole_id, "room_id" => $room_id]
		));
		if (!empty($room)) {
			$qnt = $room->quantity;
		}
		return $qnt;
	}

	public function GetTransactionByOrderId($order_id)
	{
		if ($order_id) {
			$transaction = PaymentTransactionModel::findFirstByOrder_id($order_id);
		}

		return !empty($transaction) ? $transaction : null;
	}

	public function GetHostingByUserId($user_id)
	{
		if (!empty($user_id)) {
			$hosting = HostingModel::init()->GetHostingItem([
				'where' => 'HS.user_id = :user_id:',
				'bind' 	=> ['user_id' => $user_id],
			]);
		}
		return !empty($hosting) ? $hosting : '';
	}

	public function CountVillaItemByVillaId($villa_id)
	{
		$count = 0;
		$villa = HostingVillaModel::findFirstById($villa_id);
		if (!empty($villa)) {
			$list_villa_item = HostingVillaItemModel::findByVilla_id($villa_id);
			$count = $list_villa_item->count();
		}
		return $count;
	}

	public function GetRoomByVillaItemId($villa_item_id = null)
	{
		$return = '';
		$listRoom = HostingVillaItemRoomModel::findByVilla_item_id($villa_item_id);
		$arrRoom = [];

		if (!empty($listRoom) && $listRoom->count() > 0) {
			foreach ($listRoom as $key => $value) {
				$arrRoom[$key] = $value->toArray();
				$listBed = HostingVillaItemBedModel::find([
					'conditions' => 'room_id = :room_id:',
					'bind' => [
						'room_id' => $value->id
					]
				]);
				$arrRoom[$key]['list_bed'] = $listBed;
			}
		}

		if (!empty($arrRoom)) {
			foreach ($arrRoom as $key => $value) {
				$bed = '';
				if (!empty($value['list_bed'])) {
					foreach ($value['list_bed'] as $bedItem) {
						$bed .= $bedItem->bed->title . '<strong> x ' . $bedItem->quantity . '</strong>, ';
					}
				}
				$bed = rtrim($bed, ', ');
				$return .= '<li>
											<i class="fa-solid fa-house-circle-exclamation"></i>' . $value['title'] . '<strong> x ' . $value['quantity'] . '</strong><br>
											<span><i class="fa-solid fa-bed"></i>' . $bed . '</span>
										</li>';
			}
		}

		return $return;
	}

	public function GetPropsByVillaId($villa_id = null)
	{
		$return = '';
		$listProps = HostingVillaPropertiesModel::find([
			'conditions' => 'villa_id = :villa_id:',
			'bind' => [
				'villa_id' => $villa_id
			]
		]);
		if (!empty($listProps)) {
			foreach ($listProps as $key => $value) {
				if ($key <= 6) {
					$return .= '<li>
											' . $value->properties->icon . '
											<span>' . $value->properties->title . '</span>
										</li>';
				}
			}
		}
		return $return;
	}

	public function GetPropsByRoomId($room_id = null)
	{
		$return = '';
		$listProps = HostingRoomPropertiesModel::find([
			'conditions' => 'room_id = :room_id:',
			'bind' => [
				'room_id' => $room_id
			]
		]);
		if (!empty($listProps)) {
			$return = $listProps;
		}
		return $return;
	}

	public function GetQntBedByRoomId($room_id = null, $bed_id = null)
	{
		$return = 0;
		$objBed = HostingRoomBedModel::findFirst([
			'conditions' => 'room_id = :room_id: AND bed_id = :bed_id:',
			'bind' => [
				'room_id' => $room_id,
				'bed_id' => $bed_id
			]
		]);
		if (!empty($objBed)) {
			$return = $objBed->quantity;
		}
		return $return;
	}

	public function autoVer($filePath)
	{
		$filePath = PUBLIC_PATH . $filePath;
		if (file_exists($filePath)) {
			return filemtime($filePath);
		}
		return time();
	}

	public function checkHostingPremium($hosting_id = null)
	{
		if (empty($hosting_id) || $hosting_id <= 0) {
			return '';
		}

		$premium = HostingPremiumModel::findFirstByHosting_id($hosting_id);
		if ($premium && $premium->premium_type == 'vip') {
			return 'true';
		}
		return '';
	}

	public function getVillaItemForBooking($booking_id, $villa_item_id)
	{
		$bookingItemVilla = BookingVillaModel::findFirst([
			'conditions'	=> "booking_id = :booking_id: AND villa_item_id = :villa_item_id:",
			'bind'				=> ['booking_id' => $booking_id, 'villa_item_id' => $villa_item_id]
		]);
		return !empty($bookingItemVilla) ? $bookingItemVilla : null;
	}

	public function getVerifyStatus($identifier, $user_id)
	{
		$userVerify = UserVerifyModel::findFirst([
			'conditions' 	=> 'identifier = :identifier: AND user_id = :user_id:',
			'bind' 				=> [
				'identifier' 	=> $identifier,
				'user_id' 		=> $user_id
			]
		]);
		return !empty($userVerify) && $userVerify->is_verify == 1 ? true : false;
	}

	public function numInWords($num)
	{
		$nwords = array(
			0                   => 'không',
			1                   => 'một',
			2                   => 'hai',
			3                   => 'ba',
			4                   => 'bốn',
			5                   => 'năm',
			6                   => 'sáu',
			7                   => 'bảy',
			8                   => 'tám',
			9                   => 'chín',
			10                  => 'mười',
			11                  => 'mười một',
			12                  => 'mười hai',
			13                  => 'mười ba',
			14                  => 'mười bốn',
			15                  => 'mười lăm',
			16                  => 'mười sáu',
			17                  => 'mười bảy',
			18                  => 'mười tám',
			19                  => 'mười chín',
			20                  => 'hai mươi',
			30                  => 'ba mươi',
			40                  => 'bốn mươi',
			50                  => 'năm mươi',
			60                  => 'sáu mươi',
			70                  => 'bảy mươi',
			80                  => 'tám mươi',
			90                  => 'chín mươi',
			100                 => 'trăm',
			1000                => 'nghìn',
			1000000             => 'triệu',
			1000000000          => 'tỷ',
			1000000000000       => 'nghìn tỷ',
			1000000000000000    => 'ngàn triệu triệu',
			1000000000000000000 => 'tỷ tỷ',
		);
		$separate = ' ';
		$negative = ' âm ';
		$rltTen   = ' linh ';
		$decimal  = ' phẩy ';
		if (!is_numeric($num)) {
			$w = '#';
		} else if ($num < 0) {
			$w = $negative . $this->numInWords(abs($num));
		} else {
			if (fmod($num, 1) != 0) {
				$numInstr    = strval($num);
				$numInstrArr = explode(".", $numInstr);
				$w           = $this->numInWords(intval($numInstrArr[0])) . $decimal . $this->numInWords(intval($numInstrArr[1]));
			} else {
				$w = '';
				if ($num < 21) // 0 to 20
				{
					$w .= $nwords[$num];
				} else if ($num < 100) {
					// 21 to 99
					$w .= $nwords[10 * floor($num / 10)];
					$r = fmod($num, 10);
					if ($r > 0) {
						$w .= $separate . $nwords[$r];
					}
				} else if ($num < 1000) {
					// 100 to 999
					$w .= $nwords[floor($num / 100)] . $separate . $nwords[100];
					$r = fmod($num, 100);
					if ($r > 0) {
						if ($r < 10) {
							$w .= $rltTen . $separate . $this->numInWords($r);
						} else {
							$w .= $separate . $this->numInWords($r);
						}
					}
				} else {
					$baseUnit     = pow(1000, floor(log($num, 1000)));
					$numBaseUnits = (int) ($num / $baseUnit);
					$r            = fmod($num, $baseUnit);
					if ($r == 0) {
						$w = $this->numInWords($numBaseUnits) . $separate . $nwords[$baseUnit];
					} else {
						if ($r < 100) {
							if ($r >= 10) {
								$w = $this->numInWords($numBaseUnits) . $separate . $nwords[$baseUnit] . ' không trăm ' . $this->numInWords($r);
							} else {
								$w = $this->numInWords($numBaseUnits) . $separate . $nwords[$baseUnit] . ' không trăm linh ' . $this->numInWords($r);
							}
						} else {
							$baseUnitInstr      = strval($baseUnit);
							$rInstr             = strval($r);
							$lenOfBaseUnitInstr = strlen($baseUnitInstr);
							$lenOfRInstr        = strlen($rInstr);
							if (($lenOfBaseUnitInstr - 1) != $lenOfRInstr) {
								$numberOfZero = $lenOfBaseUnitInstr - $lenOfRInstr - 1;
								if ($numberOfZero == 2) {
									$w = $this->numInWords($numBaseUnits) . $separate . $nwords[$baseUnit] . ' không trăm linh ' . $this->numInWords($r);
								} else if ($numberOfZero == 1) {
									$w = $this->numInWords($numBaseUnits) . $separate . $nwords[$baseUnit] . ' không trăm ' . $this->numInWords($r);
								} else {
									$w = $this->numInWords($numBaseUnits) . $separate . $nwords[$baseUnit] . $separate . $this->numInWords($r);
								}
							} else {
								$w = $this->numInWords($numBaseUnits) . $separate . $nwords[$baseUnit] . $separate . $this->numInWords($r);
							}
						}
					}
				}
			}
		}
		return $w;
	}

	public function numberInVietnameseWords($num)
	{
		return str_replace("mươi năm", "mươi lăm", str_replace("mươi một", "mươi mốt", $this->numInWords($num)));
	}

	public function numberInVietnameseCurrency($num)
	{
		$rs    = $this->numberInVietnameseWords($num);
		$rs[0] = strtoupper($rs[0]);
		return $rs . ' đồng';
	}

	public function checkAllowBookingOnline($hosting_id = null)
	{
		// Kiểm tra hosting_id hợp lệ
		if (!$hosting_id || $hosting_id <= 0) {
			return false;
		}

		// Kiểm tra hosting và trạng thái booking online
		$hosting = HostingModel::findFirstById($hosting_id);
		if (empty($hosting) || $hosting->is_booking_online == 0) {
			return false;
		}

		// Kiểm tra cấu hình hệ thống
		$systemConfig = SystemConfigModel::findFirstByCode('allow-booking-online');
		return !$systemConfig || $systemConfig->data === 'true';
	}

	public function getDefaultData($type = null)
	{
		switch ($type) {
			case 'province':
				$list = LocationProvinceModel::find([
					'conditions' 	=> 'status = :status:',
					'bind' 				=> [
						'status' 				=> 1,
					],
					'order' 			=> 'position ASC',
				]);
				break;
			case 'hosting_type':
				$list = HostingTypeModel::find([
					'conditions' 	=> 'status = :status:',
					'bind' 				=> [
						'status' 				=> 1,
					],
					'order' 			=> 'position ASC',
				]);
				break;
			case 'room_type':
				$list = HostingRoomTypeModel::find([
					'conditions' 	=> 'status = :status:',
					'bind' 				=> [
						'status' 				=> 1,
					],
					'order' 			=> 'position ASC',
				]);
				break;
			case 'props_group':
				$list = HostingPropertiesGroupModel::findByStatus(1);
				break;
			default:
				$list = [];
				break;
		}
		return !empty($list) ? $list : [];
	}

	public function getListLocationFeature()
	{
		$list_province_featured = LocationProvinceModel::find([
			'conditions' 	=> 'status = :status: and featured = :featured:',
			'bind' 				=> [
				'status' 				=> 1,
				'featured' 				=> 1,
			],
			'order' 			=> 'position ASC',
		]);

		$list_district_featured = LocationDistrictModel::find([
			'conditions' 	=> 'featured = :featured:',
			'bind' 				=> [
				'featured' 				=> 1,
			],
		]);

		$arr = array_merge($list_province_featured->toArray(), $list_district_featured->toArray());

		usort($arr, function ($a, $b) {
			return $a['position'] <=> $b['position'];
		});

		$mergedObject = json_decode(json_encode($arr), false);
		return !empty($mergedObject) ? $mergedObject : [];
	}

	public function getArrSupporterForBooking()
	{
		$arrSupporterId = array_column(ErpMemberModel::find([
			'columns' => 'id',
			'conditions' => 'department_id = :department_id: AND status = :status: AND is_working = :is_working:',
			'bind' => ['department_id' => ErpMemberModel::SUPPORTER_DEPARTMENT_ID, 'status' => 1, 'is_working' => 1],
			'order' => 'id ASC',
		])->toArray(), 'id');

		return $arrSupporterId;
	}

	public function getSupporter()
	{
		// Kiểm tra refCode từ cookie
		if (!empty($refCode = $this->cookies->get('refCode')->getValue())) {
			$supporter = ErpMemberModel::findFirst([
				'conditions' => 'code = :code: AND status = :status:',
				'bind' => ['code' => $refCode, 'status' => 1],
			]);
			if ($supporter) {
				$this->session->set('supporter_id', $supporter->id);
				return $supporter;
			}
		}

		// Kiểm tra supporter_id từ session
		if (!empty($supporterId = $this->session->get('supporter_id'))) {
			return ErpMemberModel::findFirstById($supporterId);
		}

		// Lấy danh sách supporter hợp lệ
		$arrSupporterId = $this->getArrSupporterForBooking();
		if (empty($arrSupporterId)) {
			return null;
		}

		// Lấy supporter tiếp theo từ log
		$lastNextSupporter = $this->getLastNextSupporter();
		if (!empty($lastNextSupporter) && !empty($lastNextSupporter->supporter_next_id)) {
			$nextSupporterId = $lastNextSupporter->supporter_next_id;
			// Nếu supporter tiếp theo hợp lệ thì trả về
			$nextSupporter = ErpMemberModel::findFirstById($nextSupporterId);
			if (in_array($nextSupporterId, $arrSupporterId) && $nextSupporter->is_working == 1) {
				$supporter = ErpMemberModel::findFirstById($nextSupporterId);
				if ($supporter) {
					$this->session->set('supporter_id', $supporter->id);
					return $supporter;
				}
			}
		}
		// Chọn ngẫu nhiên một supporter từ danh sách
		$randomSupporterId = $arrSupporterId[mt_rand(0, count($arrSupporterId) - 1)];
		$supporter = ErpMemberModel::findFirstById($randomSupporterId);
		if ($supporter) {
			$this->session->set('supporter_id', $supporter->id);
		}
		return $supporter;
	}

	public function getReferenceMember()
	{
		$refCode = $this->cookies->get('refCode')->getValue();
		if (empty($refCode)) {
			return null;
		}

		return ErpMemberModel::findFirstByCode($refCode);
	}

	public function getLastNextSupporter()
	{
		$lastNextSupporter = BookingGuestLogModel::findFirst([
			'order' => 'id DESC'
		]);

		return $lastNextSupporter;
	}

	public function getVoucherWelcome()
	{
		$code = trim($this->sysConfig('voucher-welcome', true));

		if (empty($code)) {
			return null;
		}

		$voucher = EventVoucherModel::findFirst([
			'conditions' 	=> 'code = :code: and status = :status: and start_date <= :start_date: and end_date >= :end_date:',
			'bind' 				=> ['code' => $code, 'status' => 1, 'start_date' => date('Y-m-d'), 'end_date' => date('Y-m-d')],
		]);

		return !empty($voucher) ? $voucher : null;
	}

	public function getBestValueVoucher($basePrice = 0, $user = null)
	{
		$basePrice = (int) $basePrice;
		if (empty($basePrice) || $basePrice <= 0) {
			return null;
		}

		$currentDate 	= date('Y-m-d');
		$conditions 	= 'status = :status: AND start_date <= :current_date: AND end_date >= :current_date:';
		$bind = [
			'status' 				=> 1,
			'current_date' 	=> $currentDate
		];

		if (empty($user)) {
			$conditions 				.= ' AND source_type = :source_type:';
			$bind['source_type'] = 'system';
		}

		$vouchers = EventVoucherModel::find([
			'conditions' => $conditions,
			'bind' => $bind
		]);

		if ($vouchers->count() == 0) {
			return null;
		}

		$bestVoucher = null;
		$maxDiscount = 0;

		foreach ($vouchers as $voucher) {
			$discountValue = 0;

			if ($voucher->type == 'percent') {
				$discountValue = $basePrice * ($voucher->value / 100);
			} else {
				$discountValue = $voucher->value;
			}

			if ($discountValue > $maxDiscount) {
				$maxDiscount = $discountValue;
				$bestVoucher = $voucher;
			}
		}

		return $bestVoucher;
	}

	public function getPriceWithVoucher($price = 0, $voucher = null)
	{
		$price = (int) $price;
		if (empty($voucher) || empty($price) || $price <= 0) {
			return $price;
		}

		if ($voucher->type == 'percent') {
			return $price * (1 - $voucher->value / 100);
		} else {
			return $price - $voucher->value;
		}

		return $price;
	}

	public function assignRandomSupporter()
	{
		$arrSupporterId = $this->getArrSupporterForBooking();

		$arrMemberId = array_column(BookingModel::find([
			'columns' 		=> 'member_id',
			'conditions' 	=> 'channel = :channel: AND member_id IS NOT NULL AND member_id != 0',
			'bind' 				=> ['channel' => 'website'],
			'order' 			=> 'id DESC',
			'limit' 			=> count($arrSupporterId) - 1
		])->toArray(), 'member_id');

		$arrOnlySupporterId 	= array_diff($arrSupporterId, $arrMemberId);
		$supporterId 					= $arrOnlySupporterId[array_rand($arrOnlySupporterId)];
		return $supporterId;
	}

	public function getDateRange($startDate = null, $endDate = null)
	{
		$dateRange = [];
		for($date = $startDate; $date < $endDate; $date = date('Y-m-d', strtotime($date . ' +1 day'))) {
			$dateRange[] = $date;
		}
		return $dateRange;
	}

	public function getListRoomType()
	{
		$listRoomType = HostingRoomTypeModel::find([
			'conditions' => 'status = :status:',
			'bind' => ['status' => 1]
		]);

		return $listRoomType;
	}

	public function getListProps()
	{
		$listProps = HostingPropertiesGroupModel::find([
			'conditions' => 'status = :status:',
			'bind' => ['status' => 1]
		]);

		return $listProps;
	}

	public function calcPercent($value, $total)
	{
		return round($value / $total * 100, 2);
	}
}
