<?php
/**
 * <PERSON><PERSON> dụ sử dụng các hàm chức năng mới trong BookingReviewModel
 */

// Giả sử trong controller hoặc service
use Modules\App\Models\BookingReviewModel;

class ExampleUsage 
{
    public function getHostingReviewData($hostingId)
    {
        $reviewModel = new BookingReviewModel();
        
        // 1. Lấy điểm đánh giá tổng thể
        $overallRating = $reviewModel->getHostingOverallRating($hostingId);
        echo "Điểm tổng thể: " . ($overallRating ?? 'Chưa có đánh giá') . "\n";
        
        // 2. Lấy thống kê chi tiết
        $ratingStats = $reviewModel->calculateHostingRating($hostingId);
        if ($ratingStats) {
            echo "Chi tiết đánh giá:\n";
            echo "- Tổng số đánh giá: {$ratingStats['total_reviews']}\n";
            echo "- <PERSON><PERSON><PERSON><PERSON> sạch sẽ: {$ratingStats['clean_avg']}\n";
            echo "- Điểm nhân viên: {$ratingStats['staff_avg']}\n";
            echo "- Điểm tiện nghi: {$ratingStats['amenities_avg']}\n";
            echo "- Điểm vị trí: {$ratingStats['location_avg']}\n";
            echo "- Điểm giá trị: {$ratingStats['value_avg']}\n";
        }
        
        // 3. Lấy phân bố điểm
        $distribution = $reviewModel->getHostingRatingDistribution($hostingId);
        echo "Phân bố điểm:\n";
        for ($i = 5; $i >= 1; $i--) {
            echo "- {$i} sao: {$distribution[$i]} đánh giá\n";
        }
        
        // 4. Lấy đánh giá mới nhất
        $latestReviews = $reviewModel->getLatestReviews($hostingId, 5);
        echo "5 đánh giá mới nhất:\n";
        foreach ($latestReviews as $review) {
            echo "- {$review->getUserDisplayName()}: {$review->overall} sao - {$review->getFormattedDate()}\n";
            echo "  Nội dung: " . substr($review->content, 0, 100) . "...\n";
            
            $images = $review->getParsedImages();
            if (!empty($images)) {
                echo "  Có " . count($images) . " hình ảnh\n";
            }
            
            if ($review->traveler_type) {
                echo "  Loại khách: {$review->getTravelerTypeLabel()}\n";
            }
            echo "\n";
        }
        
        // 5. So sánh với hosting cùng loại
        $comparison = $reviewModel->compareWithSimilarHostings($hostingId);
        if ($comparison) {
            echo "So sánh với hosting cùng loại:\n";
            echo "- Điểm hiện tại: {$comparison['current']['overall_avg']}\n";
            echo "- Điểm trung bình cùng loại: {$comparison['similar_avg']['overall_avg']}\n";
            $diff = $comparison['comparison']['overall'];
            echo "- Chênh lệch: " . ($diff > 0 ? '+' : '') . "{$diff} điểm\n";
        }
        
        // 6. Từ khóa được nhắc nhiều nhất
        $keywords = $reviewModel->getMostMentionedKeywords($hostingId, 5);
        echo "Từ khóa phổ biến:\n";
        foreach ($keywords as $word => $count) {
            echo "- '{$word}': {$count} lần\n";
        }
        
        // 7. Thống kê theo tháng
        $monthlyStats = $reviewModel->getMonthlyRatingStats($hostingId, 6);
        echo "Thống kê 6 tháng gần đây:\n";
        foreach ($monthlyStats as $stat) {
            echo "- {$stat['month']}/{$stat['year']}: {$stat['total_reviews']} đánh giá, TB: {$stat['avg_rating']}\n";
        }
        
        return [
            'overall_rating' => $overallRating,
            'rating_stats' => $ratingStats,
            'distribution' => $distribution,
            'latest_reviews' => $latestReviews,
            'comparison' => $comparison,
            'keywords' => $keywords,
            'monthly_stats' => $monthlyStats
        ];
    }
    
    public function updateRatingAfterNewReview($hostingId)
    {
        $reviewModel = new BookingReviewModel();
        
        // Cập nhật bảng hosting_rating
        $result = $reviewModel->updateHostingRatingTable($hostingId);
        
        if ($result) {
            echo "Đã cập nhật thành công bảng hosting_rating cho hosting ID: {$hostingId}\n";
        } else {
            echo "Lỗi khi cập nhật bảng hosting_rating\n";
        }
        
        return $result;
    }
    
    public function getReviewsByType($hostingId, $travelerType)
    {
        $reviewModel = new BookingReviewModel();
        
        // Lấy đánh giá theo loại khách
        $reviews = $reviewModel->getReviewsByTravelerType($hostingId, $travelerType, 10);
        
        echo "Đánh giá từ khách {$travelerType}:\n";
        foreach ($reviews as $review) {
            echo "- {$review->getUserDisplayName()}: {$review->overall} sao\n";
            echo "  {$review->content}\n\n";
        }
        
        return $reviews;
    }
}

// Ví dụ sử dụng trong controller
/*
class HostingController extends ControllerBase 
{
    public function detailAction($hostingId)
    {
        // ... code khác ...
        
        $reviewModel = new BookingReviewModel();
        
        // Load dữ liệu cho view
        $this->view->hosting_reviews = $reviewModel->getLatestReviews($hostingId, 10);
        $this->view->rating_stats = $reviewModel->calculateHostingRating($hostingId);
        $this->view->rating_distribution = $reviewModel->getHostingRatingDistribution($hostingId);
        
        // ... code khác ...
    }
    
    public function reviewsApiAction()
    {
        $hostingId = $this->request->get('hosting_id');
        $page = $this->request->get('page', 'int', 1);
        $limit = 10;
        $offset = ($page - 1) * $limit;
        
        $reviewModel = new BookingReviewModel();
        $reviews = $reviewModel->getReviewsByHosting($hostingId, [
            'limit' => $limit,
            'offset' => $offset
        ]);
        
        $data = [];
        foreach ($reviews as $review) {
            $data[] = [
                'id' => $review->id,
                'user_name' => $review->getUserDisplayName(),
                'avatar_initials' => $review->getAvatarWithName(),
                'rating' => $review->overall,
                'title' => $review->title,
                'content' => $review->content,
                'images' => $review->getParsedImages(),
                'traveler_type' => $review->getTravelerTypeLabel(),
                'created_date' => $review->getFormattedDate(),
                'created_ago' => $review->getFormattedDate('d/m/Y H:i')
            ];
        }
        
        return $this->response->setJsonContent([
            'status' => 'success',
            'data' => $data,
            'pagination' => [
                'page' => $page,
                'limit' => $limit,
                'total' => count($reviews)
            ]
        ]);
    }
}
*/

// Ví dụ sử dụng trong Volt template
/*
<!-- Trong file review.volt -->
{% for review in hosting_reviews %}
<div class="review-item">
    <div class="review-avatar">
        <div class="review-avatar__fallback">
            {{ review.getAvatarWithName() }}
        </div>
    </div>
    <div class="review-content">
        <h4>{{ review.getUserDisplayName() }}</h4>
        <div class="rating">{{ review.overall }} sao</div>
        <p>{{ review.content }}</p>
        <div class="review-meta">
            <span>{{ review.getFormattedDate() }}</span>
            {% if review.traveler_type %}
                <span>{{ review.getTravelerTypeLabel() }}</span>
            {% endif %}
        </div>
        {% set images = review.getParsedImages() %}
        {% if images|length > 0 %}
            <div class="review-images">
                {% for img in images %}
                    <img src="{{ img }}" alt="Review image">
                {% endfor %}
            </div>
        {% endif %}
    </div>
</div>
{% endfor %}
*/
?>
